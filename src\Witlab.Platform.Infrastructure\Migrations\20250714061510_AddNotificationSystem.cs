﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Witlab.Platform.Infrastructure.Migrations;

/// <inheritdoc />
public partial class AddNotificationSystem : Migration
{
  /// <inheritdoc />
  protected override void Up(MigrationBuilder migrationBuilder)
  {
    migrationBuilder.CreateTable(
        name: "Notifications",
        columns: table => new
        {
          Id = table.Column<Guid>(type: "TEXT", nullable: false),
          UserId = table.Column<Guid>(type: "TEXT", nullable: false),
          Title = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
          Content = table.Column<string>(type: "TEXT", maxLength: 1000, nullable: false),
          Type = table.Column<int>(type: "INTEGER", nullable: false),
          Category = table.Column<int>(type: "INTEGER", nullable: false),
          Priority = table.Column<int>(type: "INTEGER", nullable: false),
          Status = table.Column<int>(type: "INTEGER", nullable: false),
          SourceType = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
          SourceId = table.Column<string>(type: "TEXT", maxLength: 100, nullable: true),
          Data = table.Column<string>(type: "TEXT", maxLength: 4000, nullable: true),
          ExpiresAt = table.Column<DateTime>(type: "TEXT", nullable: true),
          ReadAt = table.Column<DateTime>(type: "TEXT", nullable: true),
          Created = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
          CreatedBy = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
          LastModified = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
          LastModifiedBy = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true)
        },
        constraints: table =>
        {
          table.PrimaryKey("PK_Notifications", x => x.Id);
          table.ForeignKey(
                    name: "FK_Notifications_Users_UserId",
                    column: x => x.UserId,
                    principalTable: "Users",
                    principalColumn: "Id",
                    onDelete: ReferentialAction.Cascade);
        });

    migrationBuilder.CreateTable(
        name: "NotificationTemplates",
        columns: table => new
        {
          Id = table.Column<Guid>(type: "TEXT", nullable: false),
          Code = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
          Name = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
          Title = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
          Content = table.Column<string>(type: "TEXT", maxLength: 2000, nullable: false),
          Type = table.Column<int>(type: "INTEGER", nullable: false),
          Category = table.Column<int>(type: "INTEGER", nullable: false),
          Priority = table.Column<int>(type: "INTEGER", nullable: false),
          IsActive = table.Column<bool>(type: "INTEGER", nullable: false, defaultValue: true),
          Created = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
          CreatedBy = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true),
          LastModified = table.Column<DateTimeOffset>(type: "TEXT", nullable: false),
          LastModifiedBy = table.Column<string>(type: "TEXT", maxLength: 50, nullable: true)
        },
        constraints: table =>
        {
          table.PrimaryKey("PK_NotificationTemplates", x => x.Id);
        });

    migrationBuilder.CreateIndex(
        name: "IX_Notifications_Created",
        table: "Notifications",
        column: "Created");

    migrationBuilder.CreateIndex(
        name: "IX_Notifications_ExpiresAt",
        table: "Notifications",
        column: "ExpiresAt");

    migrationBuilder.CreateIndex(
        name: "IX_Notifications_UserId",
        table: "Notifications",
        column: "UserId");

    migrationBuilder.CreateIndex(
        name: "IX_Notifications_UserId_Status",
        table: "Notifications",
        columns: new[] { "UserId", "Status" });

    migrationBuilder.CreateIndex(
        name: "IX_NotificationTemplates_Category",
        table: "NotificationTemplates",
        column: "Category");

    migrationBuilder.CreateIndex(
        name: "IX_NotificationTemplates_Code",
        table: "NotificationTemplates",
        column: "Code",
        unique: true);

    migrationBuilder.CreateIndex(
        name: "IX_NotificationTemplates_IsActive",
        table: "NotificationTemplates",
        column: "IsActive");
  }

  /// <inheritdoc />
  protected override void Down(MigrationBuilder migrationBuilder)
  {
    migrationBuilder.DropTable(
        name: "Notifications");

    migrationBuilder.DropTable(
        name: "NotificationTemplates");
  }
}
