namespace Witlab.Platform.Infrastructure.Notifications.Services;

/// <summary>
/// 通知缓存服务接口
/// </summary>
public interface INotificationCacheService
{
  /// <summary>
  /// 获取用户未读通知数量
  /// </summary>
  Task<int?> GetUnreadCountAsync(Guid userId);

  /// <summary>
  /// 设置用户未读通知数量
  /// </summary>
  Task SetUnreadCountAsync(Guid userId, int count, TimeSpan? expiry = null);

  /// <summary>
  /// 增加用户未读通知数量
  /// </summary>
  Task IncrementUnreadCountAsync(Guid userId);

  /// <summary>
  /// 减少用户未读通知数量
  /// </summary>
  Task DecrementUnreadCountAsync(Guid userId);

  /// <summary>
  /// 清除用户未读通知数量缓存
  /// </summary>
  Task ClearUnreadCountAsync(Guid userId);

  /// <summary>
  /// 获取通知模板缓存
  /// </summary>
  Task<string?> GetTemplateAsync(string templateCode);

  /// <summary>
  /// 设置通知模板缓存
  /// </summary>
  Task SetTemplateAsync(string templateCode, string templateData, TimeSpan? expiry = null);

  /// <summary>
  /// 清除通知模板缓存
  /// </summary>
  Task ClearTemplateAsync(string templateCode);

  /// <summary>
  /// 设置用户连接信息
  /// </summary>
  Task SetUserConnectionAsync(Guid userId, string connectionId);

  /// <summary>
  /// 获取用户连接信息
  /// </summary>
  Task<string?> GetUserConnectionAsync(Guid userId);

  /// <summary>
  /// 清除用户连接信息
  /// </summary>
  Task ClearUserConnectionAsync(Guid userId);
}
