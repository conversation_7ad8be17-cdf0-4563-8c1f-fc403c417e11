using Witlab.Platform.Core.Platform.Services;

namespace Witlab.Platform.UseCases.Platform.Notifications.MarkRead;

/// <summary>
/// 标记通知为已读命令处理器
/// </summary>
public class MarkNotificationAsReadHandler : IRequestHandler<MarkNotificationAsReadCommand, Result>
{
  private readonly INotificationService _notificationService;
  private readonly ILogger<MarkNotificationAsReadHandler> _logger;

  public MarkNotificationAsReadHandler(
    INotificationService notificationService,
    ILogger<MarkNotificationAsReadHandler> logger)
  {
    _notificationService = notificationService;
    _logger = logger;
  }

  public async Task<Result> Handle(MarkNotificationAsReadCommand request, CancellationToken cancellationToken)
  {
    try
    {
      _logger.LogInformation("开始标记通知为已读，通知ID: {NotificationId}，用户ID: {UserId}", 
        request.NotificationId, request.UserId);

      var result = await _notificationService.MarkAsReadAsync(request.NotificationId, request.UserId);

      if (!result.IsSuccess)
      {
        _logger.LogWarning("标记通知为已读失败，通知ID: {NotificationId}，用户ID: {UserId}，错误: {Errors}", 
          request.NotificationId, request.UserId, string.Join(", ", result.Errors));
        return Result.Error(new ErrorList(result.Errors));
      }

      _logger.LogInformation("标记通知为已读成功，通知ID: {NotificationId}，用户ID: {UserId}", 
        request.NotificationId, request.UserId);

      return Result.Success();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "标记通知为已读时发生异常，通知ID: {NotificationId}，用户ID: {UserId}", 
        request.NotificationId, request.UserId);
      return Result.Error("标记通知为已读时发生系统错误");
    }
  }
}
