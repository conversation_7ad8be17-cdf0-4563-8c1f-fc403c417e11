using Witlab.Platform.Core.Platform.Services;

namespace Witlab.Platform.UseCases.Platform.Notifications.Get;

/// <summary>
/// 获取未读通知数量查询处理器
/// </summary>
public class GetUnreadCountHandler : IRequestHandler<GetUnreadCountQuery, Result<int>>
{
  private readonly INotificationService _notificationService;
  private readonly ILogger<GetUnreadCountHandler> _logger;

  public GetUnreadCountHandler(
    INotificationService notificationService,
    ILogger<GetUnreadCountHandler> logger)
  {
    _notificationService = notificationService;
    _logger = logger;
  }

  public async Task<Result<int>> Handle(GetUnreadCountQuery request, CancellationToken cancellationToken)
  {
    try
    {
      _logger.LogDebug("开始获取用户未读通知数量，用户ID: {UserId}", request.UserId);

      var result = await _notificationService.GetUnreadCountAsync(request.UserId);

      if (!result.IsSuccess)
      {
        _logger.LogWarning("获取用户未读通知数量失败，用户ID: {UserId}，错误: {Errors}", 
          request.UserId, string.Join(", ", result.Errors));
        return Result.Error(new ErrorList(result.Errors));
      }

      _logger.LogDebug("获取用户未读通知数量成功，用户ID: {UserId}，数量: {Count}", 
        request.UserId, result.Value);

      return Result.Success(result.Value);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取用户未读通知数量时发生异常，用户ID: {UserId}", request.UserId);
      return Result.Error("获取用户未读通知数量时发生系统错误");
    }
  }
}
