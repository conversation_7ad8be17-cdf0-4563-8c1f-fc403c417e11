using Witlab.Platform.Core.Platform.Services;
using Witlab.Platform.UseCases.Platform.Notifications.DTOs;

namespace Witlab.Platform.UseCases.Platform.Notifications.Templates;

/// <summary>
/// 创建通知模板命令处理器
/// </summary>
public class CreateTemplateHandler : IRequestHandler<CreateTemplateCommand, Result<CreateNotificationTemplateResponse>>
{
  private readonly INotificationTemplateService _templateService;
  private readonly ILogger<CreateTemplateHandler> _logger;

  public CreateTemplateHandler(
    INotificationTemplateService templateService,
    ILogger<CreateTemplateHandler> logger)
  {
    _templateService = templateService;
    _logger = logger;
  }

  public async Task<Result<CreateNotificationTemplateResponse>> Handle(CreateTemplateCommand request, CancellationToken cancellationToken)
  {
    try
    {
      _logger.LogInformation("开始创建通知模板，代码: {Code}，名称: {Name}", request.Code, request.Name);

      var result = await _templateService.CreateTemplateAsync(
        request.Code,
        request.Name,
        request.Title,
        request.Content,
        request.Type,
        request.Category,
        request.Priority);

      if (!result.IsSuccess)
      {
        _logger.LogWarning("创建通知模板失败，代码: {Code}，错误: {Errors}", 
          request.Code, string.Join(", ", result.Errors));
        return Result.Error(new ErrorList(result.Errors));
      }

      var template = result.Value;
      var response = new CreateNotificationTemplateResponse(
        template.Id,
        template.Code,
        template.Name);

      _logger.LogInformation("通知模板创建成功，模板ID: {TemplateId}，代码: {Code}", 
        template.Id, request.Code);

      return Result.Success(response);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "创建通知模板时发生异常，代码: {Code}", request.Code);
      return Result.Error("创建通知模板时发生系统错误");
    }
  }
}
