﻿using System.Text.Json.Serialization;

namespace Witlab.Platform.Core.Platform.ValueObjects;

/// <summary>
/// 会话信息值对象
/// </summary>
public class SessionInfo : ValueObject
{
  /// <summary>
  /// IP地址
  /// </summary>
  public string IpAddress { get; private set; } = string.Empty;

  /// <summary>
  /// User-Agent信息
  /// </summary>
  public string UserAgent { get; private set; } = string.Empty;

  /// <summary>
  /// 设备类型
  /// </summary>
  public string DeviceType { get; private set; } = string.Empty;

  /// <summary>
  /// 操作系统
  /// </summary>
  public string OperatingSystem { get; private set; } = string.Empty;

  /// <summary>
  /// 浏览器信息
  /// </summary>
  public string Browser { get; private set; } = string.Empty;

  /// <summary>
  /// 地理位置（可选）
  /// </summary>
  public string? Location { get; private set; }

  /// <summary>
  /// 时区
  /// </summary>
  public string TimeZone { get; private set; } = string.Empty;

  /// <summary>
  /// 语言设置
  /// </summary>
  public string Language { get; private set; } = string.Empty;

  public SessionInfo()
  {
    // for efcore
  }

  [JsonConstructor]
  public SessionInfo(
    string ipAddress,
    string userAgent,
    string deviceType = "",
    string operatingSystem = "",
    string browser = "",
    string? location = null,
    string timeZone = "",
    string language = "")
  {
    IpAddress = Guard.Against.NullOrEmpty(ipAddress, nameof(ipAddress));
    UserAgent = Guard.Against.NullOrEmpty(userAgent, nameof(userAgent));
    DeviceType = deviceType;
    OperatingSystem = operatingSystem;
    Browser = browser;
    Location = location;
    TimeZone = timeZone;
    Language = language;
  }

  /// <summary>
  /// 从HTTP请求创建会话信息
  /// </summary>
  /// <param name="ipAddress">IP地址</param>
  /// <param name="userAgent">User-Agent</param>
  /// <param name="acceptLanguage">Accept-Language头</param>
  /// <returns>会话信息</returns>
  public static SessionInfo FromHttpRequest(string ipAddress, string userAgent, string? acceptLanguage = null)
  {
    var deviceInfo = ParseUserAgent(userAgent);
    var language = ParseLanguage(acceptLanguage);

    return new SessionInfo(
      ipAddress,
      userAgent,
      deviceInfo.DeviceType,
      deviceInfo.OperatingSystem,
      deviceInfo.Browser,
      null, // 地理位置需要额外的服务来解析
      "", // 时区需要客户端提供
      language);
  }

  /// <summary>
  /// 解析User-Agent获取设备信息
  /// </summary>
  private static (string DeviceType, string OperatingSystem, string Browser) ParseUserAgent(string userAgent)
  {
    if (string.IsNullOrEmpty(userAgent))
      return ("Unknown", "Unknown", "Unknown");

    var deviceType = "Desktop";
    var operatingSystem = "Unknown";
    var browser = "Unknown";

    // 简单的User-Agent解析逻辑
    var ua = userAgent.ToLowerInvariant();

    // 检测移动设备
    if (ua.Contains("mobile") || ua.Contains("android") || ua.Contains("iphone") || ua.Contains("ipad"))
    {
      deviceType = "Mobile";
    }

    // 检测操作系统
    if (ua.Contains("windows"))
      operatingSystem = "Windows";
    else if (ua.Contains("mac os") || ua.Contains("macos"))
      operatingSystem = "macOS";
    else if (ua.Contains("linux"))
      operatingSystem = "Linux";
    else if (ua.Contains("android"))
      operatingSystem = "Android";
    else if (ua.Contains("ios") || ua.Contains("iphone") || ua.Contains("ipad"))
      operatingSystem = "iOS";

    // 检测浏览器
    if (ua.Contains("chrome") && !ua.Contains("edg"))
      browser = "Chrome";
    else if (ua.Contains("firefox"))
      browser = "Firefox";
    else if (ua.Contains("safari") && !ua.Contains("chrome"))
      browser = "Safari";
    else if (ua.Contains("edg"))
      browser = "Edge";
    else if (ua.Contains("opera"))
      browser = "Opera";

    return (deviceType, operatingSystem, browser);
  }

  /// <summary>
  /// 解析Accept-Language获取语言信息
  /// </summary>
  private static string ParseLanguage(string? acceptLanguage)
  {
    if (string.IsNullOrEmpty(acceptLanguage))
      return "en-US";

    // 取第一个语言设置
    var languages = acceptLanguage.Split(',');
    if (languages.Length > 0)
    {
      var primaryLanguage = languages[0].Split(';')[0].Trim();
      return primaryLanguage;
    }

    return "en-US";
  }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return IpAddress ?? string.Empty; // Ensure null safety
        yield return UserAgent ?? string.Empty; // Ensure null safety
        yield return DeviceType ?? string.Empty; // Ensure null safety
        yield return OperatingSystem ?? string.Empty; // Ensure null safety
        yield return Browser ?? string.Empty; // Ensure null safety
        yield return Location ?? string.Empty; // Ensure null safety
        yield return TimeZone ?? string.Empty; // Ensure null safety
        yield return Language ?? string.Empty; // Ensure null safety
    }

  public override string ToString()
  {
    return $"{DeviceType} - {OperatingSystem} - {Browser} ({IpAddress})";
  }
}
