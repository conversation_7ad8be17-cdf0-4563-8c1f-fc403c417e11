using Witlab.Platform.Core.Platform.Enums;
using Witlab.Platform.UseCases.Platform.Notifications.DTOs;

namespace Witlab.Platform.UseCases.Platform.Notifications.Create;

/// <summary>
/// 创建通知命令
/// </summary>
public record CreateNotificationCommand(
    Guid UserId,
    string Title,
    string Content,
    NotificationType Type,
    NotificationCategory Category,
    NotificationPriority? Priority = null,
    string? SourceType = null,
    string? SourceId = null,
    string? Data = null,
    DateTime? ExpiresAt = null
) : IRequest<Result<CreateNotificationResponse>>;
