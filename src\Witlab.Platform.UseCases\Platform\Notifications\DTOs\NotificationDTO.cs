using Witlab.Platform.Core.Platform.Enums;

namespace Witlab.Platform.UseCases.Platform.Notifications.DTOs;

/// <summary>
/// 通知数据传输对象
/// </summary>
public record NotificationDTO(
    Guid Id,
    Guid UserId,
    string Title,
    string Content,
    int TypeValue,
    string TypeName,
    int CategoryValue,
    string CategoryName,
    int PriorityValue,
    string PriorityName,
    int StatusValue,
    string StatusName,
    string? SourceType,
    string? SourceId,
    string? Data,
    DateTime? ExpiresAt,
    DateTime? ReadAt,
    DateTime Created,
    string? CreatedBy,
    DateTime LastModified,
    string? LastModifiedBy
);

/// <summary>
/// 创建通知响应
/// </summary>
public record CreateNotificationResponse(
    Guid Id,
    string Title,
    string Content,
    string Message = "通知创建成功"
);

/// <summary>
/// 通知统计信息
/// </summary>
public record NotificationStatsDTO(
    int UnreadCount,
    int TotalCount
);

/// <summary>
/// 分页通知列表响应
/// </summary>
public record PagedNotificationsResponse(
    IEnumerable<NotificationDTO> Items,
    int TotalCount,
    int PageIndex,
    int PageSize,
    bool HasNextPage,
    bool HasPreviousPage
);
