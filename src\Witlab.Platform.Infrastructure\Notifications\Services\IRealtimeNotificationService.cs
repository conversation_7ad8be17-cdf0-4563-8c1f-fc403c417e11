using Witlab.Platform.Core.Platform;

namespace Witlab.Platform.Infrastructure.Notifications.Services;

/// <summary>
/// 实时通知服务接口
/// </summary>
public interface IRealtimeNotificationService
{
  /// <summary>
  /// 发送通知给指定用户
  /// </summary>
  Task SendNotificationToUserAsync(Guid userId, Notification notification);

  /// <summary>
  /// 发送通知给指定用户组
  /// </summary>
  Task SendNotificationToUsersAsync(IEnumerable<Guid> userIds, Notification notification);

  /// <summary>
  /// 发送通知给角色组
  /// </summary>
  Task SendNotificationToRoleAsync(string roleName, Notification notification);

  /// <summary>
  /// 发送未读数量更新给用户
  /// </summary>
  Task SendUnreadCountUpdateAsync(Guid userId, int unreadCount);

  /// <summary>
  /// 发送通知状态更新给用户
  /// </summary>
  Task SendNotificationStatusUpdateAsync(Guid userId, Guid notificationId, string status);
}
