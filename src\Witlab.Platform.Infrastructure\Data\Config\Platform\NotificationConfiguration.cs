using Witlab.Platform.Core.Platform;
using Witlab.Platform.Core.Platform.Enums;
using Witlab.Platform.Infrastructure.Data.Config.Base;

namespace Witlab.Platform.Infrastructure.Data.Config.Platform;

public class NotificationConfiguration : IEntityTypeConfiguration<Notification>
{
  public void Configure(EntityTypeBuilder<Notification> builder)
  {
    builder.HasKey(x => x.Id);

    // 配置审计字段
    builder.ConfigureAuditableProperties<Notification, Guid>();

    builder.Property(p => p.UserId)
        .IsRequired();

    builder.Property(p => p.Title)
        .HasMaxLength(200)
        .IsRequired();

    builder.Property(p => p.Content)
        .HasMaxLength(1000)
        .IsRequired();

    builder.Property(p => p.SourceType)
        .HasMaxLength(100);

    builder.Property(p => p.SourceId)
        .HasMaxLength(100);

    builder.Property(p => p.Data)
        .HasMaxLength(4000); // JSON数据

    builder.Property(p => p.ExpiresAt);

    builder.Property(p => p.ReadAt);

    // 配置NotificationType SmartEnum转换
    builder.Property(p => p.Type)
        .HasConversion(
            v => v.Value,
            v => NotificationType.FromValue(v));

    // 配置NotificationCategory SmartEnum转换
    builder.Property(p => p.Category)
        .HasConversion(
            v => v.Value,
            v => NotificationCategory.FromValue(v));

    // 配置NotificationPriority SmartEnum转换
    builder.Property(p => p.Priority)
        .HasConversion(
            v => v.Value,
            v => NotificationPriority.FromValue(v));

    // 配置NotificationStatus SmartEnum转换
    builder.Property(p => p.Status)
        .HasConversion(
            v => v.Value,
            v => NotificationStatus.FromValue(v));

    // 配置与User的关系
    builder.HasOne<User>()
        .WithMany()
        .HasForeignKey(n => n.UserId)
        .OnDelete(DeleteBehavior.Cascade);

    // 创建索引以提高查询性能
    builder.HasIndex(n => n.UserId)
        .HasDatabaseName("IX_Notifications_UserId");

    builder.HasIndex(n => new { n.UserId, n.Status })
        .HasDatabaseName("IX_Notifications_UserId_Status");

    builder.HasIndex(n => n.Created)
        .HasDatabaseName("IX_Notifications_Created");

    builder.HasIndex(n => n.ExpiresAt)
        .HasDatabaseName("IX_Notifications_ExpiresAt");

    builder.ToTable("Notifications");
  }
}
