using Microsoft.Extensions.Caching.Distributed;
using System.Text.Json;

namespace Witlab.Platform.Infrastructure.Notifications.Services;

/// <summary>
/// 通知缓存服务实现
/// </summary>
public class NotificationCacheService : INotificationCacheService
{
  private readonly IDistributedCache _cache;
  private readonly ILogger<NotificationCacheService> _logger;

  // 缓存键前缀
  private const string UNREAD_COUNT_PREFIX = "notification:unread_count:";
  private const string TEMPLATE_PREFIX = "notification:template:";
  private const string USER_CONNECTION_PREFIX = "notification:user_connections:";

  // 默认过期时间
  private static readonly TimeSpan DefaultUnreadCountExpiry = TimeSpan.FromMinutes(30);
  private static readonly TimeSpan DefaultTemplateExpiry = TimeSpan.FromHours(24);
  private static readonly TimeSpan DefaultConnectionExpiry = TimeSpan.FromHours(2);

  public NotificationCacheService(
    IDistributedCache cache,
    ILogger<NotificationCacheService> logger)
  {
    _cache = cache;
    _logger = logger;
  }

  public async Task<int?> GetUnreadCountAsync(Guid userId)
  {
    try
    {
      var key = $"{UNREAD_COUNT_PREFIX}{userId}";
      var value = await _cache.GetStringAsync(key);
      
      if (int.TryParse(value, out var count))
      {
        return count;
      }
      
      return null;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取用户未读通知数量缓存失败，用户ID: {UserId}", userId);
      return null;
    }
  }

  public async Task SetUnreadCountAsync(Guid userId, int count, TimeSpan? expiry = null)
  {
    try
    {
      var key = $"{UNREAD_COUNT_PREFIX}{userId}";
      var options = new DistributedCacheEntryOptions
      {
        AbsoluteExpirationRelativeToNow = expiry ?? DefaultUnreadCountExpiry
      };

      await _cache.SetStringAsync(key, count.ToString(), options);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "设置用户未读通知数量缓存失败，用户ID: {UserId}，数量: {Count}", userId, count);
    }
  }

  public async Task IncrementUnreadCountAsync(Guid userId)
  {
    try
    {
      var currentCount = await GetUnreadCountAsync(userId) ?? 0;
      await SetUnreadCountAsync(userId, currentCount + 1);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "增加用户未读通知数量缓存失败，用户ID: {UserId}", userId);
    }
  }

  public async Task DecrementUnreadCountAsync(Guid userId)
  {
    try
    {
      var currentCount = await GetUnreadCountAsync(userId) ?? 0;
      var newCount = Math.Max(0, currentCount - 1);
      await SetUnreadCountAsync(userId, newCount);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "减少用户未读通知数量缓存失败，用户ID: {UserId}", userId);
    }
  }

  public async Task ClearUnreadCountAsync(Guid userId)
  {
    try
    {
      var key = $"{UNREAD_COUNT_PREFIX}{userId}";
      await _cache.RemoveAsync(key);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "清除用户未读通知数量缓存失败，用户ID: {UserId}", userId);
    }
  }

  public async Task<string?> GetTemplateAsync(string templateCode)
  {
    try
    {
      var key = $"{TEMPLATE_PREFIX}{templateCode}";
      return await _cache.GetStringAsync(key);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取通知模板缓存失败，模板代码: {TemplateCode}", templateCode);
      return null;
    }
  }

  public async Task SetTemplateAsync(string templateCode, string templateData, TimeSpan? expiry = null)
  {
    try
    {
      var key = $"{TEMPLATE_PREFIX}{templateCode}";
      var options = new DistributedCacheEntryOptions
      {
        AbsoluteExpirationRelativeToNow = expiry ?? DefaultTemplateExpiry
      };

      await _cache.SetStringAsync(key, templateData, options);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "设置通知模板缓存失败，模板代码: {TemplateCode}", templateCode);
    }
  }

  public async Task ClearTemplateAsync(string templateCode)
  {
    try
    {
      var key = $"{TEMPLATE_PREFIX}{templateCode}";
      await _cache.RemoveAsync(key);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "清除通知模板缓存失败，模板代码: {TemplateCode}", templateCode);
    }
  }

  public async Task SetUserConnectionAsync(Guid userId, string connectionId)
  {
    try
    {
      var key = $"{USER_CONNECTION_PREFIX}{userId}";
      var options = new DistributedCacheEntryOptions
      {
        AbsoluteExpirationRelativeToNow = DefaultConnectionExpiry
      };

      await _cache.SetStringAsync(key, connectionId, options);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "设置用户连接信息缓存失败，用户ID: {UserId}，连接ID: {ConnectionId}", 
        userId, connectionId);
    }
  }

  public async Task<string?> GetUserConnectionAsync(Guid userId)
  {
    try
    {
      var key = $"{USER_CONNECTION_PREFIX}{userId}";
      return await _cache.GetStringAsync(key);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取用户连接信息缓存失败，用户ID: {UserId}", userId);
      return null;
    }
  }

  public async Task ClearUserConnectionAsync(Guid userId)
  {
    try
    {
      var key = $"{USER_CONNECTION_PREFIX}{userId}";
      await _cache.RemoveAsync(key);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "清除用户连接信息缓存失败，用户ID: {UserId}", userId);
    }
  }
}
