using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;
using System.Security.Claims;

namespace Witlab.Platform.Infrastructure.Notifications.SignalR;

/// <summary>
/// 通知SignalR Hub
/// </summary>
[Authorize]
public class NotificationHub : Hub
{
  private readonly ILogger<NotificationHub> _logger;

  public NotificationHub(ILogger<NotificationHub> logger)
  {
    _logger = logger;
  }

  /// <summary>
  /// 连接时加入用户组
  /// </summary>
  public override async Task OnConnectedAsync()
  {
    var userId = GetUserId();
    if (userId.HasValue)
    {
      var userGroup = $"User_{userId.Value}";
      await Groups.AddToGroupAsync(Context.ConnectionId, userGroup);
      
      _logger.LogInformation("用户连接到通知Hub，用户ID: {UserId}，连接ID: {ConnectionId}", 
        userId.Value, Context.ConnectionId);
    }

    await base.OnConnectedAsync();
  }

  /// <summary>
  /// 断开连接时离开用户组
  /// </summary>
  public override async Task OnDisconnectedAsync(Exception exception)
  {
    var userId = GetUserId();
    if (userId.HasValue)
    {
      var userGroup = $"User_{userId.Value}";
      await Groups.RemoveFromGroupAsync(Context.ConnectionId, userGroup);
      
      _logger.LogInformation("用户断开通知Hub连接，用户ID: {UserId}，连接ID: {ConnectionId}", 
        userId.Value, Context.ConnectionId);
    }

    await base.OnDisconnectedAsync(exception);
  }

  /// <summary>
  /// 加入角色组
  /// </summary>
  public async Task JoinRoleGroup(string roleName)
  {
    var roleGroup = $"Role_{roleName}";
    await Groups.AddToGroupAsync(Context.ConnectionId, roleGroup);
    
    _logger.LogInformation("用户加入角色组，角色: {RoleName}，连接ID: {ConnectionId}", 
      roleName, Context.ConnectionId);
  }

  /// <summary>
  /// 离开角色组
  /// </summary>
  public async Task LeaveRoleGroup(string roleName)
  {
    var roleGroup = $"Role_{roleName}";
    await Groups.RemoveFromGroupAsync(Context.ConnectionId, roleGroup);
    
    _logger.LogInformation("用户离开角色组，角色: {RoleName}，连接ID: {ConnectionId}", 
      roleName, Context.ConnectionId);
  }

  /// <summary>
  /// 获取当前用户ID
  /// </summary>
  private Guid? GetUserId()
  {
    var userIdClaim = Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
    if (Guid.TryParse(userIdClaim, out var userId))
    {
      return userId;
    }
    return null;
  }
}
