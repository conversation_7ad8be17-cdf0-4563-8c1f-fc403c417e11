﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Witlab.Platform.Core.Platform.Interfaces;
using Witlab.Platform.Infrastructure.Auth.Models;

namespace Witlab.Platform.Infrastructure.Auth;

/// <summary>
/// 会话清理后台服务
/// </summary>
public class SessionCleanupService : BackgroundService
{
  private readonly IServiceProvider _serviceProvider;
  private readonly ILogger<SessionCleanupService> _logger;
  private readonly SessionSettings _sessionSettings;

  public SessionCleanupService(
    IServiceProvider serviceProvider,
    ILogger<SessionCleanupService> logger,
    IOptions<SessionSettings> sessionSettings)
  {
    _serviceProvider = serviceProvider;
    _logger = logger;
    _sessionSettings = sessionSettings.Value;
  }

  protected override async Task ExecuteAsync(CancellationToken stoppingToken)
  {
    if (!_sessionSettings.EnableSessionTracking)
    {
      _logger.LogInformation("会话跟踪未启用，会话清理服务将不运行");
      return;
    }

    _logger.LogInformation("会话清理服务已启动，清理间隔: {IntervalMinutes} 分钟", _sessionSettings.CleanupIntervalMinutes);

    while (!stoppingToken.IsCancellationRequested)
    {
      try
      {
        await CleanupExpiredSessionsAsync();
        
        // 等待下一次清理
        var delay = TimeSpan.FromMinutes(_sessionSettings.CleanupIntervalMinutes);
        await Task.Delay(delay, stoppingToken);
      }
      catch (OperationCanceledException)
      {
        // 服务正在停止
        break;
      }
      catch (Exception ex)
      {
        _logger.LogError(ex, "会话清理过程中发生错误");
        
        // 发生错误时等待较短时间后重试
        await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
      }
    }

    _logger.LogInformation("会话清理服务已停止");
  }

  /// <summary>
  /// 清理过期会话
  /// </summary>
  private async Task CleanupExpiredSessionsAsync()
  {
    try
    {
      using var scope = _serviceProvider.CreateScope();
      var sessionService = scope.ServiceProvider.GetRequiredService<IUserSessionService>();

      var result = await sessionService.CleanupExpiredSessionsAsync();
      
      if (result.IsSuccess)
      {
        var cleanedCount = result.Value;
        if (cleanedCount > 0)
        {
          _logger.LogInformation("成功清理 {CleanedCount} 个过期会话", cleanedCount);
        }
        else if (_sessionSettings.EnableDetailedLogging)
        {
          _logger.LogDebug("没有发现需要清理的过期会话");
        }
      }
      else
      {
        _logger.LogWarning("清理过期会话失败: {Errors}", string.Join(", ", result.Errors));
      }
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "清理过期会话时发生异常");
    }
  }
}
