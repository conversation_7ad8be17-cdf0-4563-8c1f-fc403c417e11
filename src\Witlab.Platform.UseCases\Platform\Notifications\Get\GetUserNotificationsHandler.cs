using Witlab.Platform.Core.Platform.Services;
using Witlab.Platform.UseCases.Platform.Notifications.DTOs;

namespace Witlab.Platform.UseCases.Platform.Notifications.Get;

/// <summary>
/// 获取用户通知列表查询处理器
/// </summary>
public class GetUserNotificationsHandler : IRequestHandler<GetUserNotificationsQuery, Result<PagedNotificationsResponse>>
{
  private readonly INotificationService _notificationService;
  private readonly ILogger<GetUserNotificationsHandler> _logger;

  public GetUserNotificationsHandler(
    INotificationService notificationService,
    ILogger<GetUserNotificationsHandler> logger)
  {
    _notificationService = notificationService;
    _logger = logger;
  }

  public async Task<Result<PagedNotificationsResponse>> Handle(GetUserNotificationsQuery request, CancellationToken cancellationToken)
  {
    try
    {
      _logger.LogInformation("开始获取用户通知列表，用户ID: {UserId}，页码: {PageIndex}，页大小: {PageSize}", 
        request.UserId, request.PageIndex, request.PageSize);

      var skip = request.PageIndex * request.PageSize;
      var result = await _notificationService.GetUserNotificationsAsync(
        request.UserId,
        request.Status,
        request.Category,
        skip,
        request.PageSize);

      if (!result.IsSuccess)
      {
        _logger.LogWarning("获取用户通知列表失败，用户ID: {UserId}，错误: {Errors}", 
          request.UserId, string.Join(", ", result.Errors));
        return Result.Error(new ErrorList(result.Errors));
      }

      var notifications = result.Value;
      var notificationDTOs = notifications.Select(n => new NotificationDTO(
        n.Id,
        n.UserId,
        n.Title,
        n.Content,
        n.Type.Value,
        n.Type.Name,
        n.Category.Value,
        n.Category.Name,
        n.Priority.Value,
        n.Priority.Name,
        n.Status.Value,
        n.Status.Name,
        n.SourceType,
        n.SourceId,
        n.Data,
        n.ExpiresAt,
        n.ReadAt,
        n.Created.DateTime,
        n.CreatedBy,
        n.LastModified.DateTime,
        n.LastModifiedBy
      )).ToList();

      // 获取总数（这里简化处理，实际应该有专门的计数查询）
      var totalCount = notificationDTOs.Count;
      var hasNextPage = totalCount == request.PageSize;
      var hasPreviousPage = request.PageIndex > 0;

      var response = new PagedNotificationsResponse(
        notificationDTOs,
        totalCount,
        request.PageIndex,
        request.PageSize,
        hasNextPage,
        hasPreviousPage);

      _logger.LogInformation("获取用户通知列表成功，用户ID: {UserId}，返回数量: {Count}", 
        request.UserId, notificationDTOs.Count);

      return Result.Success(response);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取用户通知列表时发生异常，用户ID: {UserId}", request.UserId);
      return Result.Error("获取用户通知列表时发生系统错误");
    }
  }
}
