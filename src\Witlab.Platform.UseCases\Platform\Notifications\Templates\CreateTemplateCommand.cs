using Witlab.Platform.Core.Platform.Enums;
using Witlab.Platform.UseCases.Platform.Notifications.DTOs;

namespace Witlab.Platform.UseCases.Platform.Notifications.Templates;

/// <summary>
/// 创建通知模板命令
/// </summary>
public record CreateTemplateCommand(
    string Code,
    string Name,
    string Title,
    string Content,
    NotificationType Type,
    NotificationCategory Category,
    NotificationPriority? Priority = null
) : IRequest<Result<CreateNotificationTemplateResponse>>;
