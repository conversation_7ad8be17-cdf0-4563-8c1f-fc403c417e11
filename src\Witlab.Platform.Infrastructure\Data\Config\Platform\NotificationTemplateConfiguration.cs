using Witlab.Platform.Core.Platform;
using Witlab.Platform.Core.Platform.Enums;
using Witlab.Platform.Infrastructure.Data.Config.Base;

namespace Witlab.Platform.Infrastructure.Data.Config.Platform;

public class NotificationTemplateConfiguration : IEntityTypeConfiguration<NotificationTemplate>
{
  public void Configure(EntityTypeBuilder<NotificationTemplate> builder)
  {
    builder.HasKey(x => x.Id);

    // 配置审计字段
    builder.ConfigureAuditableProperties<NotificationTemplate, Guid>();

    builder.Property(p => p.Code)
        .HasMaxLength(100)
        .IsRequired();

    builder.Property(p => p.Name)
        .HasMaxLength(200)
        .IsRequired();

    builder.Property(p => p.Title)
        .HasMaxLength(500)
        .IsRequired();

    builder.Property(p => p.Content)
        .HasMaxLength(2000)
        .IsRequired();

    builder.Property(p => p.IsActive)
        .IsRequired()
        .HasDefaultValue(true);

    // 配置NotificationType SmartEnum转换
    builder.Property(p => p.Type)
        .HasConversion(
            v => v.Value,
            v => NotificationType.FromValue(v));

    // 配置NotificationCategory SmartEnum转换
    builder.Property(p => p.Category)
        .HasConversion(
            v => v.Value,
            v => NotificationCategory.FromValue(v));

    // 配置NotificationPriority SmartEnum转换
    builder.Property(p => p.Priority)
        .HasConversion(
            v => v.Value,
            v => NotificationPriority.FromValue(v));

    // 确保Code字段唯一
    builder.HasIndex(t => t.Code)
        .IsUnique()
        .HasDatabaseName("IX_NotificationTemplates_Code");

    // 创建索引以提高查询性能
    builder.HasIndex(t => t.IsActive)
        .HasDatabaseName("IX_NotificationTemplates_IsActive");

    builder.HasIndex(t => t.Category)
        .HasDatabaseName("IX_NotificationTemplates_Category");

    builder.ToTable("NotificationTemplates");
  }
}
