namespace Witlab.Platform.Core.Platform.Events;

/// <summary>
/// 通知模板创建事件
/// </summary>
public class NotificationTemplateCreatedEvent : DomainEventBase
{
  public NotificationTemplateCreatedEvent(Guid templateId, string code, string name)
  {
    TemplateId = templateId;
    Code = code;
    Name = name;
  }

  /// <summary>
  /// 模板ID
  /// </summary>
  public Guid TemplateId { get; }

  /// <summary>
  /// 模板代码
  /// </summary>
  public string Code { get; }

  /// <summary>
  /// 模板名称
  /// </summary>
  public string Name { get; }
}
