using Witlab.Platform.Core.Platform.Services;
using Witlab.Platform.UseCases.Platform.Notifications.DTOs;

namespace Witlab.Platform.UseCases.Platform.Notifications.Templates;

/// <summary>
/// 获取通知模板列表查询处理器
/// </summary>
public class GetTemplatesHandler : IRequestHandler<GetTemplatesQuery, Result<IEnumerable<NotificationTemplateDTO>>>
{
  private readonly INotificationTemplateService _templateService;
  private readonly ILogger<GetTemplatesHandler> _logger;

  public GetTemplatesHandler(
    INotificationTemplateService templateService,
    ILogger<GetTemplatesHandler> logger)
  {
    _templateService = templateService;
    _logger = logger;
  }

  public async Task<Result<IEnumerable<NotificationTemplateDTO>>> Handle(GetTemplatesQuery request, CancellationToken cancellationToken)
  {
    try
    {
      _logger.LogInformation("开始获取通知模板列表，IsActive: {IsActive}，Category: {Category}", 
        request.IsActive, request.Category?.Name);

      var result = await _templateService.GetTemplatesAsync(
        request.IsActive,
        request.Category,
        request.Skip,
        request.Take);

      if (!result.IsSuccess)
      {
        _logger.LogWarning("获取通知模板列表失败，错误: {Errors}", 
          string.Join(", ", result.Errors));
        return Result.Error(new ErrorList(result.Errors));
      }

      var templates = result.Value;
      var templateDTOs = templates.Select(t => new NotificationTemplateDTO(
        t.Id,
        t.Code,
        t.Name,
        t.Title,
        t.Content,
        t.Type.Value,
        t.Type.Name,
        t.Category.Value,
        t.Category.Name,
        t.Priority.Value,
        t.Priority.Name,
        t.IsActive,
        t.Created.DateTime,
        t.CreatedBy,
        t.LastModified.DateTime,
        t.LastModifiedBy
      ));

      _logger.LogInformation("获取通知模板列表成功，返回数量: {Count}", templates.Count());

      return Result.Success(templateDTOs);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取通知模板列表时发生异常");
      return Result.Error("获取通知模板列表时发生系统错误");
    }
  }
}
