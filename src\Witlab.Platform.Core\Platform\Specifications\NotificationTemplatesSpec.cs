using Witlab.Platform.Core.Platform.Enums;

namespace Witlab.Platform.Core.Platform.Specifications;

/// <summary>
/// 通知模板查询规约（支持状态和分类过滤）
/// </summary>
public class NotificationTemplatesSpec : Specification<NotificationTemplate>
{
  public NotificationTemplatesSpec(
    bool? isActive = null, 
    NotificationCategory? category = null, 
    int skip = 0, 
    int take = 50)
  {
    if (isActive.HasValue)
    {
      Query.Where(t => t.IsActive == isActive.Value);
    }

    if (category.HasValue)
    {
      Query.Where(t => t.Category == category.Value);
    }

    Query.OrderBy(t => t.Name)
         .Skip(skip)
         .Take(take);
  }
}
