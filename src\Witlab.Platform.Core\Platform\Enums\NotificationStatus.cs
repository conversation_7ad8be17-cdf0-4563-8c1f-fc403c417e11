namespace Witlab.Platform.Core.Platform.Enums;

/// <summary>
/// 通知状态
/// </summary>
public class NotificationStatus : SmartEnum<NotificationStatus>
{
  /// <summary>
  /// 未读
  /// </summary>
  public static readonly NotificationStatus Unread = new(nameof(Unread), 0);

  /// <summary>
  /// 已读
  /// </summary>
  public static readonly NotificationStatus Read = new(nameof(Read), 1);

  /// <summary>
  /// 已归档
  /// </summary>
  public static readonly NotificationStatus Archived = new(nameof(Archived), 2);

  protected NotificationStatus(string name, int value) : base(name, value) { }
}
