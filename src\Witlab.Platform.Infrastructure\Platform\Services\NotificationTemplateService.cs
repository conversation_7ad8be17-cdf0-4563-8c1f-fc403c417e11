using Witlab.Platform.Core.Platform;
using Witlab.Platform.Core.Platform.Enums;
using Witlab.Platform.Core.Platform.Services;
using Witlab.Platform.Core.Platform.Specifications;

namespace Witlab.Platform.Infrastructure.Platform.Services;

/// <summary>
/// 通知模板领域服务实现
/// </summary>
public class NotificationTemplateService : INotificationTemplateService
{
  private readonly IRepository<NotificationTemplate> _templateRepository;
  private readonly ILogger<NotificationTemplateService> _logger;

  public NotificationTemplateService(
    IRepository<NotificationTemplate> templateRepository,
    ILogger<NotificationTemplateService> logger)
  {
    _templateRepository = templateRepository;
    _logger = logger;
  }

  public async Task<Result<NotificationTemplate>> CreateTemplateAsync(
    string code, 
    string name, 
    string title, 
    string content, 
    NotificationType type, 
    NotificationCategory category, 
    NotificationPriority priority = null)
  {
    try
    {
      // 检查代码是否已存在
      var existsResult = await IsTemplateCodeExistsAsync(code);
      if (existsResult.IsSuccess && existsResult.Value)
      {
        return Result.Error($"模板代码已存在: {code}");
      }

      var template = new NotificationTemplate(
        code, 
        name, 
        title, 
        content, 
        type, 
        category, 
        priority ?? NotificationPriority.Normal);

      await _templateRepository.AddAsync(template);
      await _templateRepository.SaveChangesAsync();

      _logger.LogInformation("通知模板创建成功，模板ID: {TemplateId}，代码: {Code}", 
        template.Id, code);

      return Result.Success(template);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "创建通知模板时发生异常，代码: {Code}", code);
      return Result.Error("创建通知模板失败");
    }
  }

  public async Task<Result<NotificationTemplate>> UpdateTemplateAsync(
    Guid templateId, 
    string name, 
    string title, 
    string content, 
    NotificationType type, 
    NotificationCategory category, 
    NotificationPriority priority)
  {
    try
    {
      var template = await _templateRepository.GetByIdAsync(templateId);
      if (template == null)
      {
        return Result.Error("通知模板不存在");
      }

      template.Update(name, title, content, type, category, priority);
      await _templateRepository.UpdateAsync(template);
      await _templateRepository.SaveChangesAsync();

      _logger.LogInformation("通知模板更新成功，模板ID: {TemplateId}", templateId);

      return Result.Success(template);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "更新通知模板时发生异常，模板ID: {TemplateId}", templateId);
      return Result.Error("更新通知模板失败");
    }
  }

  public async Task<Result<NotificationTemplate>> GetTemplateByCodeAsync(string code)
  {
    try
    {
      var spec = new NotificationTemplateByCodeSpec(code);
      var template = await _templateRepository.FirstOrDefaultAsync(spec);

      if (template == null)
      {
        return Result.Error($"通知模板不存在: {code}");
      }

      return Result.Success(template);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "根据代码获取通知模板时发生异常，代码: {Code}", code);
      return Result.Error("获取通知模板失败");
    }
  }

  public async Task<Result<NotificationTemplate>> GetTemplateByIdAsync(Guid templateId)
  {
    try
    {
      var template = await _templateRepository.GetByIdAsync(templateId);
      if (template == null)
      {
        return Result.Error("通知模板不存在");
      }

      return Result.Success(template);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "根据ID获取通知模板时发生异常，模板ID: {TemplateId}", templateId);
      return Result.Error("获取通知模板失败");
    }
  }

  public async Task<Result<IEnumerable<NotificationTemplate>>> GetActiveTemplatesAsync()
  {
    try
    {
      var spec = new ActiveNotificationTemplatesSpec();
      var templates = await _templateRepository.ListAsync(spec);

      return Result.Success(templates.AsEnumerable());
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取活跃通知模板时发生异常");
      return Result.Error("获取活跃通知模板失败");
    }
  }

  public async Task<Result<IEnumerable<NotificationTemplate>>> GetTemplatesAsync(
    bool? isActive = null, 
    NotificationCategory? category = null, 
    int skip = 0, 
    int take = 50)
  {
    try
    {
      var spec = new NotificationTemplatesSpec(isActive, category, skip, take);
      var templates = await _templateRepository.ListAsync(spec);

      return Result.Success(templates.AsEnumerable());
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取通知模板列表时发生异常");
      return Result.Error("获取通知模板列表失败");
    }
  }

  public async Task<Result> ActivateTemplateAsync(Guid templateId)
  {
    try
    {
      var template = await _templateRepository.GetByIdAsync(templateId);
      if (template == null)
      {
        return Result.Error("通知模板不存在");
      }

      template.Activate();
      await _templateRepository.UpdateAsync(template);
      await _templateRepository.SaveChangesAsync();

      _logger.LogInformation("通知模板启用成功，模板ID: {TemplateId}", templateId);

      return Result.Success();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "启用通知模板时发生异常，模板ID: {TemplateId}", templateId);
      return Result.Error("启用通知模板失败");
    }
  }

  public async Task<Result> DeactivateTemplateAsync(Guid templateId)
  {
    try
    {
      var template = await _templateRepository.GetByIdAsync(templateId);
      if (template == null)
      {
        return Result.Error("通知模板不存在");
      }

      template.Deactivate();
      await _templateRepository.UpdateAsync(template);
      await _templateRepository.SaveChangesAsync();

      _logger.LogInformation("通知模板禁用成功，模板ID: {TemplateId}", templateId);

      return Result.Success();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "禁用通知模板时发生异常，模板ID: {TemplateId}", templateId);
      return Result.Error("禁用通知模板失败");
    }
  }

  public async Task<Result> DeleteTemplateAsync(Guid templateId)
  {
    try
    {
      var template = await _templateRepository.GetByIdAsync(templateId);
      if (template == null)
      {
        return Result.Error("通知模板不存在");
      }

      await _templateRepository.DeleteAsync(template);
      await _templateRepository.SaveChangesAsync();

      _logger.LogInformation("通知模板删除成功，模板ID: {TemplateId}", templateId);

      return Result.Success();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "删除通知模板时发生异常，模板ID: {TemplateId}", templateId);
      return Result.Error("删除通知模板失败");
    }
  }

  public async Task<Result<bool>> IsTemplateCodeExistsAsync(string code, Guid? excludeId = null)
  {
    try
    {
      var spec = new NotificationTemplateByCodeSpec(code);
      var template = await _templateRepository.FirstOrDefaultAsync(spec);

      var exists = template != null && (!excludeId.HasValue || template.Id != excludeId.Value);
      return Result.Success(exists);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "检查通知模板代码是否存在时发生异常，代码: {Code}", code);
      return Result.Error("检查通知模板代码失败");
    }
  }
}
