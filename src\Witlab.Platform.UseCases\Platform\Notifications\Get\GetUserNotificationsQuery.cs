using Witlab.Platform.Core.Platform.Enums;
using Witlab.Platform.UseCases.Platform.Notifications.DTOs;

namespace Witlab.Platform.UseCases.Platform.Notifications.Get;

/// <summary>
/// 获取用户通知列表查询
/// </summary>
public record GetUserNotificationsQuery(
    Guid UserId,
    NotificationStatus? Status = null,
    NotificationCategory? Category = null,
    int PageIndex = 0,
    int PageSize = 20
) : IRequest<Result<PagedNotificationsResponse>>;
