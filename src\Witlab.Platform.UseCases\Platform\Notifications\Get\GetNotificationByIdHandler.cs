using Witlab.Platform.Core.Platform.Services;
using Witlab.Platform.UseCases.Platform.Notifications.DTOs;

namespace Witlab.Platform.UseCases.Platform.Notifications.Get;

/// <summary>
/// 根据ID获取通知查询处理器
/// </summary>
public class GetNotificationByIdHandler : IRequestHandler<GetNotificationByIdQuery, Result<NotificationDTO>>
{
  private readonly INotificationService _notificationService;
  private readonly ILogger<GetNotificationByIdHandler> _logger;

  public GetNotificationByIdHandler(
    INotificationService notificationService,
    ILogger<GetNotificationByIdHandler> logger)
  {
    _notificationService = notificationService;
    _logger = logger;
  }

  public async Task<Result<NotificationDTO>> Handle(GetNotificationByIdQuery request, CancellationToken cancellationToken)
  {
    try
    {
      _logger.LogInformation("开始获取通知详情，通知ID: {NotificationId}，用户ID: {UserId}", 
        request.NotificationId, request.UserId);

      var result = await _notificationService.GetNotificationByIdAsync(request.NotificationId, request.UserId);

      if (!result.IsSuccess)
      {
        _logger.LogWarning("获取通知详情失败，通知ID: {NotificationId}，用户ID: {UserId}，错误: {Errors}", 
          request.NotificationId, request.UserId, string.Join(", ", result.Errors));
        return Result.Error(new ErrorList(result.Errors));
      }

      var notification = result.Value;
      var notificationDTO = new NotificationDTO(
        notification.Id,
        notification.UserId,
        notification.Title,
        notification.Content,
        notification.Type.Value,
        notification.Type.Name,
        notification.Category.Value,
        notification.Category.Name,
        notification.Priority.Value,
        notification.Priority.Name,
        notification.Status.Value,
        notification.Status.Name,
        notification.SourceType,
        notification.SourceId,
        notification.Data,
        notification.ExpiresAt,
        notification.ReadAt,
        notification.Created.DateTime,
        notification.CreatedBy,
        notification.LastModified.DateTime,
        notification.LastModifiedBy
      );

      _logger.LogInformation("获取通知详情成功，通知ID: {NotificationId}，用户ID: {UserId}", 
        request.NotificationId, request.UserId);

      return Result.Success(notificationDTO);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取通知详情时发生异常，通知ID: {NotificationId}，用户ID: {UserId}", 
        request.NotificationId, request.UserId);
      return Result.Error("获取通知详情时发生系统错误");
    }
  }
}
