﻿using Witlab.Platform.Core.Platform.Enums;
using Witlab.Platform.Core.Platform.Events;

namespace Witlab.Platform.Core.Platform;

/// <summary>
/// 通知实体
/// </summary>
public class Notification : AuditableEntityBase<Guid>, IAggregateRoot
{
  private Notification()
  {
  }

  public Notification(Guid userId, string title, string content, NotificationType type, NotificationCategory category, NotificationPriority? priority, string? sourceType, string? sourceId, string? data, DateTime? expiresAt = null)
  {
    Id = Guid.NewGuid();
    UserId = userId;
    Title = Guard.Against.NullOrEmpty(title, nameof(title));
    Content = Guard.Against.NullOrEmpty(content, nameof(content));
    Type = type ?? NotificationType.Info;
    Category = category ?? NotificationCategory.System;
    Priority = priority ?? NotificationPriority.Normal;
    Status = NotificationStatus.Unread;
    SourceType = sourceType;
    SourceId = sourceId;
    Data = data;
    ExpiresAt = expiresAt;

    RegisterDomainEvent(new NotificationCreatedEvent(Id, UserId, Title, Content, Type, Category, Priority));
  }

  /// <summary>
  /// 接收通知的用户ID
  /// </summary>
  public Guid UserId { get; private set; }

  /// <summary>
  /// 通知标题
  /// </summary>
  public string Title { get; private set; } = string.Empty;

  /// <summary>
  /// 通知内容
  /// </summary>
  public string Content { get; private set; } = string.Empty;

  /// <summary>
  /// 通知类型
  /// </summary>
  public NotificationType Type { get; private set; } = NotificationType.Info;

  /// <summary>
  /// 通知分类
  /// </summary>
  public NotificationCategory Category { get; private set; } = NotificationCategory.System;

  /// <summary>
  /// 优先级
  /// </summary>
  public NotificationPriority Priority { get; private set; } = NotificationPriority.Normal;

  /// <summary>
  /// 通知状态
  /// </summary>
  public NotificationStatus Status { get; private set; } = NotificationStatus.Unread;

  /// <summary>
  /// 来源类型
  /// </summary>
  public string? SourceType { get; private set; }

  /// <summary>
  /// 来源ID
  /// </summary>
  public string? SourceId { get; private set; }

  /// <summary>
  /// 额外数据（JSON格式）
  /// </summary>
  public string? Data { get; private set; }

  /// <summary>
  /// 过期时间
  /// </summary>
  public DateTime? ExpiresAt { get; private set; }

  /// <summary>
  /// 阅读时间
  /// </summary>
  public DateTime? ReadAt { get; private set; }

  /// <summary>
  /// 标记为已读
  /// </summary>
  public void MarkAsRead()
  {
    if (Status == NotificationStatus.Unread)
    {
      Status = NotificationStatus.Read;
      ReadAt = DateTime.UtcNow;
      RegisterDomainEvent(new NotificationReadEvent(Id, UserId));
    }
  }

  /// <summary>
  /// 归档通知
  /// </summary>
  public void Archive()
  {
    Status = NotificationStatus.Archived;
  }

  /// <summary>
  /// 检查通知是否已过期
  /// </summary>
  public bool IsExpired => ExpiresAt.HasValue && ExpiresAt.Value < DateTime.UtcNow;

  /// <summary>
  /// 更新通知内容
  /// </summary>
  public void UpdateContent(string title, string content)
  {
    Title = Guard.Against.NullOrEmpty(title, nameof(title));
    Content = Guard.Against.NullOrEmpty(content, nameof(content));
  }
}
