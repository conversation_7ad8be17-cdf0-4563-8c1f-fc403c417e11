﻿using Witlab.Platform.Core.Platform.Enums;
using Witlab.Platform.Core.Platform.Events;

namespace Witlab.Platform.Core.Platform;

/// <summary>
/// 通知模板实体
/// </summary>
public class NotificationTemplate : AuditableEntityBase<Guid>, IAggregateRoot
{
  private NotificationTemplate()
  {
  }

  public NotificationTemplate(string code, string name, string title, string content, NotificationType type, NotificationCategory category, NotificationPriority? priority)
  {
    Id = Guid.NewGuid();
    Code = Guard.Against.NullOrEmpty(code, nameof(code));
    Name = Guard.Against.NullOrEmpty(name, nameof(name));
    Title = Guard.Against.NullOrEmpty(title, nameof(title));
    Content = Guard.Against.NullOrEmpty(content, nameof(content));
    Type = type ?? NotificationType.Info;
    Category = category ?? NotificationCategory.System;
    Priority = priority ?? NotificationPriority.Normal;
    IsActive = true;

    RegisterDomainEvent(new NotificationTemplateCreatedEvent(Id, Code, Name));
  }

  /// <summary>
  /// 模板代码（唯一标识）
  /// </summary>
  public string Code { get; private set; } = string.Empty;

  /// <summary>
  /// 模板名称
  /// </summary>
  public string Name { get; private set; } = string.Empty;

  /// <summary>
  /// 标题模板
  /// </summary>
  public string Title { get; private set; } = string.Empty;

  /// <summary>
  /// 内容模板（支持占位符）
  /// </summary>
  public string Content { get; private set; } = string.Empty;

  /// <summary>
  /// 通知类型
  /// </summary>
  public NotificationType Type { get; private set; } = NotificationType.Info;

  /// <summary>
  /// 通知分类
  /// </summary>
  public NotificationCategory Category { get; private set; } = NotificationCategory.System;

  /// <summary>
  /// 优先级
  /// </summary>
  public NotificationPriority Priority { get; private set; } = NotificationPriority.Normal;

  /// <summary>
  /// 是否启用
  /// </summary>
  public bool IsActive { get; private set; } = true;

  /// <summary>
  /// 更新模板
  /// </summary>
  public void Update(string name, string title, string content, NotificationType type, NotificationCategory category, NotificationPriority priority)
  {
    Name = Guard.Against.NullOrEmpty(name, nameof(name));
    Title = Guard.Against.NullOrEmpty(title, nameof(title));
    Content = Guard.Against.NullOrEmpty(content, nameof(content));
    Type = type ?? NotificationType.Info;
    Category = category ?? NotificationCategory.System;
    Priority = priority ?? NotificationPriority.Normal;
  }

  /// <summary>
  /// 启用模板
  /// </summary>
  public void Activate()
  {
    IsActive = true;
  }

  /// <summary>
  /// 禁用模板
  /// </summary>
  public void Deactivate()
  {
    IsActive = false;
  }

  /// <summary>
  /// 根据参数生成通知内容
  /// </summary>
  public (string title, string content) GenerateContent(Dictionary<string, string>? parameters)
  {
    var generatedTitle = Title;
    var generatedContent = Content;

    if (parameters != null)
    {
      foreach (var param in parameters)
      {
        var placeholder = $"{{{param.Key}}}";
        generatedTitle = generatedTitle.Replace(placeholder, param.Value);
        generatedContent = generatedContent.Replace(placeholder, param.Value);
      }
    }

    return (generatedTitle, generatedContent);
  }
}
