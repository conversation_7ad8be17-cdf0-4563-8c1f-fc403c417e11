﻿using Witlab.Platform.Core.Platform.Enums;

namespace Witlab.Platform.Core.Platform.Services;

/// <summary>
/// 通知领域服务接口
/// </summary>
public interface INotificationService
{
  /// <summary>
  /// 创建通知
  /// </summary>
  Task<Result<Notification>> CreateNotificationAsync(
    Guid userId, 
    string title, 
    string content, 
    NotificationType type, 
    NotificationCategory category, 
    NotificationPriority? priority, 
    string? sourceType, 
    string? sourceId, 
    string? data, 
    DateTime? expiresAt = null);

  /// <summary>
  /// 根据模板创建通知
  /// </summary>
  Task<Result<Notification>> CreateNotificationFromTemplateAsync(
    string templateCode, 
    Guid userId, 
    Dictionary<string, string>? parameters, 
    NotificationPriority? priority, 
    string? sourceType, 
    string? sourceId, 
    string? data, 
    DateTime? expiresAt = null);

  /// <summary>
  /// 标记通知为已读
  /// </summary>
  Task<Result> MarkAsReadAsync(Guid notificationId, Guid userId);

  /// <summary>
  /// 标记用户所有通知为已读
  /// </summary>
  Task<Result> MarkAllAsReadAsync(Guid userId);

  /// <summary>
  /// 归档通知
  /// </summary>
  Task<Result> ArchiveNotificationAsync(Guid notificationId, Guid userId);

  /// <summary>
  /// 获取用户未读通知数量
  /// </summary>
  Task<Result<int>> GetUnreadCountAsync(Guid userId);

  /// <summary>
  /// 删除过期通知
  /// </summary>
  Task<Result<int>> DeleteExpiredNotificationsAsync();

  /// <summary>
  /// 获取用户通知列表
  /// </summary>
  Task<Result<IEnumerable<Notification>>> GetUserNotificationsAsync(
    Guid userId, 
    NotificationStatus? status = null, 
    NotificationCategory? category = null, 
    int skip = 0, 
    int take = 20);

  /// <summary>
  /// 根据ID获取通知
  /// </summary>
  Task<Result<Notification>> GetNotificationByIdAsync(Guid notificationId, Guid userId);

  /// <summary>
  /// 删除通知
  /// </summary>
  Task<Result> DeleteNotificationAsync(Guid notificationId, Guid userId);
}
