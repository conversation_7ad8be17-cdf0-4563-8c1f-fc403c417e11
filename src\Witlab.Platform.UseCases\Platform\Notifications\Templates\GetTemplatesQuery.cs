using Witlab.Platform.Core.Platform.Enums;
using Witlab.Platform.UseCases.Platform.Notifications.DTOs;

namespace Witlab.Platform.UseCases.Platform.Notifications.Templates;

/// <summary>
/// 获取通知模板列表查询
/// </summary>
public record GetTemplatesQuery(
    bool? IsActive = null,
    NotificationCategory? Category = null,
    int Skip = 0,
    int Take = 50
) : IRequest<Result<IEnumerable<NotificationTemplateDTO>>>;
