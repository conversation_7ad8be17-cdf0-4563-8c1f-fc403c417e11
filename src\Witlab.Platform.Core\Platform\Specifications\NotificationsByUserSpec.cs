using Witlab.Platform.Core.Platform.Enums;

namespace Witlab.Platform.Core.Platform.Specifications;

/// <summary>
/// 根据用户ID查询通知规约（支持状态和分类过滤）
/// </summary>
public class NotificationsByUserSpec : Specification<Notification>
{
  public NotificationsByUserSpec(
    Guid userId, 
    NotificationStatus? status = null, 
    NotificationCategory? category = null, 
    int skip = 0, 
    int take = 20)
  {
    Query.Where(n => n.UserId == userId);

    if (status.HasValue)
    {
      Query.Where(n => n.Status == status.Value);
    }

    if (category.HasValue)
    {
      Query.Where(n => n.Category == category.Value);
    }

    Query.OrderByDescending(n => n.Created)
         .Skip(skip)
         .Take(take);
  }
}
