namespace Witlab.Platform.Core.Platform.Enums;

/// <summary>
/// 通知类型
/// </summary>
public class NotificationType : SmartEnum<NotificationType>
{
  /// <summary>
  /// 信息通知
  /// </summary>
  public static readonly NotificationType Info = new(nameof(Info), 0);

  /// <summary>
  /// 警告通知
  /// </summary>
  public static readonly NotificationType Warning = new(nameof(Warning), 1);

  /// <summary>
  /// 错误通知
  /// </summary>
  public static readonly NotificationType Error = new(nameof(Error), 2);

  /// <summary>
  /// 成功通知
  /// </summary>
  public static readonly NotificationType Success = new(nameof(Success), 3);

  protected NotificationType(string name, int value) : base(name, value) { }
}
