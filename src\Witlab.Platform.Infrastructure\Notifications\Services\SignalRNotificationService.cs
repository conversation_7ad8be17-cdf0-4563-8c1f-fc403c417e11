using Microsoft.AspNetCore.SignalR;
using Witlab.Platform.Core.Platform;
using Witlab.Platform.Infrastructure.Notifications.SignalR;

namespace Witlab.Platform.Infrastructure.Notifications.Services;

/// <summary>
/// SignalR实时通知服务实现
/// </summary>
public class SignalRNotificationService : IRealtimeNotificationService
{
  private readonly IHubContext<NotificationHub> _hubContext;
  private readonly ILogger<SignalRNotificationService> _logger;

  public SignalRNotificationService(
    IHubContext<NotificationHub> hubContext,
    ILogger<SignalRNotificationService> logger)
  {
    _hubContext = hubContext;
    _logger = logger;
  }

  public async Task SendNotificationToUserAsync(Guid userId, Notification notification)
  {
    try
    {
      var userGroup = $"User_{userId}";
      var notificationData = CreateNotificationData(notification);

      await _hubContext.Clients.Group(userGroup)
        .SendAsync("ReceiveNotification", notificationData);

      _logger.LogInformation("实时通知发送成功，用户ID: {UserId}，通知ID: {NotificationId}", 
        userId, notification.Id);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "发送实时通知失败，用户ID: {UserId}，通知ID: {NotificationId}", 
        userId, notification.Id);
    }
  }

  public async Task SendNotificationToUsersAsync(IEnumerable<Guid> userIds, Notification notification)
  {
    try
    {
      var userGroups = userIds.Select(id => $"User_{id}").ToList();
      var notificationData = CreateNotificationData(notification);

      await _hubContext.Clients.Groups(userGroups)
        .SendAsync("ReceiveNotification", notificationData);

      _logger.LogInformation("批量实时通知发送成功，用户数量: {UserCount}，通知ID: {NotificationId}", 
        userIds.Count(), notification.Id);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "批量发送实时通知失败，用户数量: {UserCount}，通知ID: {NotificationId}", 
        userIds.Count(), notification.Id);
    }
  }

  public async Task SendNotificationToRoleAsync(string roleName, Notification notification)
  {
    try
    {
      var roleGroup = $"Role_{roleName}";
      var notificationData = CreateNotificationData(notification);

      await _hubContext.Clients.Group(roleGroup)
        .SendAsync("ReceiveNotification", notificationData);

      _logger.LogInformation("角色组实时通知发送成功，角色: {RoleName}，通知ID: {NotificationId}", 
        roleName, notification.Id);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "发送角色组实时通知失败，角色: {RoleName}，通知ID: {NotificationId}", 
        roleName, notification.Id);
    }
  }

  public async Task SendUnreadCountUpdateAsync(Guid userId, int unreadCount)
  {
    try
    {
      var userGroup = $"User_{userId}";

      await _hubContext.Clients.Group(userGroup)
        .SendAsync("UnreadCountUpdate", new { UnreadCount = unreadCount });

      _logger.LogDebug("未读数量更新发送成功，用户ID: {UserId}，未读数量: {UnreadCount}", 
        userId, unreadCount);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "发送未读数量更新失败，用户ID: {UserId}", userId);
    }
  }

  public async Task SendNotificationStatusUpdateAsync(Guid userId, Guid notificationId, string status)
  {
    try
    {
      var userGroup = $"User_{userId}";

      await _hubContext.Clients.Group(userGroup)
        .SendAsync("NotificationStatusUpdate", new 
        { 
          NotificationId = notificationId, 
          Status = status 
        });

      _logger.LogDebug("通知状态更新发送成功，用户ID: {UserId}，通知ID: {NotificationId}，状态: {Status}", 
        userId, notificationId, status);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "发送通知状态更新失败，用户ID: {UserId}，通知ID: {NotificationId}", 
        userId, notificationId);
    }
  }

  /// <summary>
  /// 创建通知数据对象
  /// </summary>
  private object CreateNotificationData(Notification notification)
  {
    return new
    {
      Id = notification.Id,
      Title = notification.Title,
      Content = notification.Content,
      Type = notification.Type.Name,
      Category = notification.Category.Name,
      Priority = notification.Priority.Name,
      SourceType = notification.SourceType,
      SourceId = notification.SourceId,
      Data = notification.Data,
      Created = notification.Created.DateTime
    };
  }
}
