﻿using Witlab.Platform.Core.Platform.Enums;

namespace Witlab.Platform.Core.Platform.Services;

/// <summary>
/// 通知模板领域服务接口
/// </summary>
public interface INotificationTemplateService
{
  /// <summary>
  /// 创建通知模板
  /// </summary>
  Task<Result<NotificationTemplate>> CreateTemplateAsync(
    string code, 
    string name, 
    string title, 
    string content, 
    NotificationType type, 
    NotificationCategory category, 
    NotificationPriority? priority);

  /// <summary>
  /// 更新通知模板
  /// </summary>
  Task<Result<NotificationTemplate>> UpdateTemplateAsync(
    Guid templateId, 
    string name, 
    string title, 
    string content, 
    NotificationType type, 
    NotificationCategory category, 
    NotificationPriority priority);

  /// <summary>
  /// 根据代码获取模板
  /// </summary>
  Task<Result<NotificationTemplate>> GetTemplateByCodeAsync(string code);

  /// <summary>
  /// 根据ID获取模板
  /// </summary>
  Task<Result<NotificationTemplate>> GetTemplateByIdAsync(Guid templateId);

  /// <summary>
  /// 获取所有活跃模板
  /// </summary>
  Task<Result<IEnumerable<NotificationTemplate>>> GetActiveTemplatesAsync();

  /// <summary>
  /// 获取模板列表
  /// </summary>
  Task<Result<IEnumerable<NotificationTemplate>>> GetTemplatesAsync(
    bool? isActive = null, 
    NotificationCategory? category = null, 
    int skip = 0, 
    int take = 50);

  /// <summary>
  /// 启用模板
  /// </summary>
  Task<Result> ActivateTemplateAsync(Guid templateId);

  /// <summary>
  /// 禁用模板
  /// </summary>
  Task<Result> DeactivateTemplateAsync(Guid templateId);

  /// <summary>
  /// 删除模板
  /// </summary>
  Task<Result> DeleteTemplateAsync(Guid templateId);

  /// <summary>
  /// 检查模板代码是否存在
  /// </summary>
  Task<Result<bool>> IsTemplateCodeExistsAsync(string code, Guid? excludeId = null);
}
