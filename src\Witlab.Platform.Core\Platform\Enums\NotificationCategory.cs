namespace Witlab.Platform.Core.Platform.Enums;

/// <summary>
/// 通知分类
/// </summary>
public class NotificationCategory : SmartEnum<NotificationCategory>
{
  /// <summary>
  /// 系统通知
  /// </summary>
  public static readonly NotificationCategory System = new(nameof(System), 0);

  /// <summary>
  /// 用户通知
  /// </summary>
  public static readonly NotificationCategory User = new(nameof(User), 1);

  /// <summary>
  /// 任务通知
  /// </summary>
  public static readonly NotificationCategory Task = new(nameof(Task), 2);

  /// <summary>
  /// 安全通知
  /// </summary>
  public static readonly NotificationCategory Security = new(nameof(Security), 3);

  /// <summary>
  /// 审计通知
  /// </summary>
  public static readonly NotificationCategory Audit = new(nameof(Audit), 4);

  protected NotificationCategory(string name, int value) : base(name, value) { }
}
