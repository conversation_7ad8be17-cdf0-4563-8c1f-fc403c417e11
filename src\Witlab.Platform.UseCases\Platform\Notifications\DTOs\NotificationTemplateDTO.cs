using Witlab.Platform.Core.Platform.Enums;

namespace Witlab.Platform.UseCases.Platform.Notifications.DTOs;

/// <summary>
/// 通知模板数据传输对象
/// </summary>
public record NotificationTemplateDTO(
    Guid Id,
    string Code,
    string Name,
    string Title,
    string Content,
    int TypeValue,
    string TypeName,
    int CategoryValue,
    string CategoryName,
    int PriorityValue,
    string PriorityName,
    bool IsActive,
    DateTime Created,
    string? CreatedBy,
    DateTime LastModified,
    string? LastModifiedBy
);

/// <summary>
/// 创建通知模板响应
/// </summary>
public record CreateNotificationTemplateResponse(
    Guid Id,
    string Code,
    string Name,
    string Message = "通知模板创建成功"
);

/// <summary>
/// 更新通知模板响应
/// </summary>
public record UpdateNotificationTemplateResponse(
    Guid Id,
    string Code,
    string Name,
    string Message = "通知模板更新成功"
);
