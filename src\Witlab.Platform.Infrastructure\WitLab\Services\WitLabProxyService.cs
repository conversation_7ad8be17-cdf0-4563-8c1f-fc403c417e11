using System.IO;
using System.Net.Http.Headers;
using System.Security.Cryptography;
using System.Text;
using System.Web;
using MediatR;
using Witlab.Platform.Core.Common.Interfaces;
using Witlab.Platform.Infrastructure.WitLab.Configurations;
using Witlab.Platform.Infrastructure.WitLab.Extensions;
using Witlab.Platform.Infrastructure.WitLab.Interfaces;
using Witlab.Platform.UseCases.WitLab.LabAccessKeys.Delete;

namespace Witlab.Platform.Infrastructure.WitLab.Services;

public class WitLabProxyService : IWitLabProxyService
{
  private readonly ILogger<WitLabProxyService> _logger;
  private readonly IMediator _mediator;
  private readonly IHttpClientFactory _httpClientFactory;
  private readonly IWitLabKeyProvider _keyProvider;
  private readonly WitLabConfiguration _witLabConfiguration;
  private readonly IUser? _currentUser;
  private static readonly HashSet<string> _witlabRequiredHeaders = new()
    {
        "Content-Type",
        "SL-API-Auth",
        "SL-API-Timestamp",
        "SL-API-Signature"
    };

  public WitLabProxyService(
      ILogger<WitLabProxyService> logger,
      IMediator mediator,
      IHttpClientFactory httpClientFactory,
      IWitLabKeyProvider keyProvider,
      IOptionsSnapshot<WitLabConfiguration> witLabConfiguration,
      IUser? currentUser)
  {
    _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    _mediator = mediator ?? throw new ArgumentNullException(nameof(mediator));
    _httpClientFactory = httpClientFactory ?? throw new ArgumentNullException(nameof(httpClientFactory));
    _keyProvider = keyProvider ?? throw new ArgumentNullException(nameof(keyProvider));
    _witLabConfiguration = witLabConfiguration.Value ?? throw new ArgumentNullException(nameof(witLabConfiguration));
    _currentUser = currentUser;

    ValidateConfiguration();
  }

  private void ValidateConfiguration()
  {
    if (string.IsNullOrWhiteSpace(_witLabConfiguration.WITLAB_SERVER_HOST))
      throw new InvalidOperationException("WITLAB_SERVER_HOST is not configured");
    if (string.IsNullOrWhiteSpace(_witLabConfiguration.WITLAB_SERVER_SITE))
      throw new InvalidOperationException("WITLAB_SERVER_SITE is not configured");

    if (_currentUser == null)
    {
      if (string.IsNullOrWhiteSpace(_witLabConfiguration.WITLAB_ADMIN_API_ACCESS_KEY))
        throw new InvalidOperationException("WITLAB_ADMIN_API_ACCESS_KEY is not configured");
      if (string.IsNullOrWhiteSpace(_witLabConfiguration.WITLAB_ADMIN_API_SECRET_KEY))
        throw new InvalidOperationException("WITLAB_ADMIN_API_SECRET_KEY is not configured");
    }
  }

  public async Task<WitLabApiResponse> ForwardAsync(WitLabApiRequest request, string path, CancellationToken ct)
  {
    _logger.LogInformation("Forwarding request to WitLab Server: {Method} {Path}", request.Method, path);

    try
    {
      using var client = await GetSignaturedWitLabClientAsync(request.Method, path);
      using var httpRequest = CreateHttpRequestMessage(request, path);

      using var httpResponse = await client.SendAsync(httpRequest, ct);

      if (httpResponse.StatusCode == System.Net.HttpStatusCode.Unauthorized)
      {
        if (_currentUser?.UserName is { Length: > 0 } userName &&
            _currentUser.DeptCode is { Length: > 0 } deptCode)
        {
          await _mediator.Send(new DeleteLabAccessKeysCommand(userName, deptCode, _currentUser.RoleCode));
        }
      }

      var response = await httpResponse.ToWitLabApiResponse(ct);
      _logger.LogInformation("Received response from WitLab Server: {StatusCode}", response.StatusCode);

      return response;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "Error forwarding request to WitLab Server: {Method} {Path}", request.Method, path);
      throw;
    }
  }

  private HttpRequestMessage CreateHttpRequestMessage(WitLabApiRequest request, string path)
  {
    var uri = new Uri($"{_witLabConfiguration.WITLAB_SERVER_HOST.TrimEnd('/')}/" +
                      $"{HttpUtility.UrlDecode(_witLabConfiguration.WITLAB_SERVER_SITE)}/rest.web.api/" +
                      $"{HttpUtility.UrlDecode(path)}");

    var httpRequestMessage = new HttpRequestMessage(new HttpMethod(request.Method), uri)
    {
      Content = new StreamContent(request.Body)
    };

    foreach (var header in request.Headers)
    {
      // 优先添加到 Headers，若失败则尝试添加到 Content.Headers
      if (!httpRequestMessage.Headers.TryAddWithoutValidation(header.Key, header.Value.ToArray()))
      {
        httpRequestMessage.Content?.Headers.TryAddWithoutValidation(header.Key, header.Value.ToArray());
      }
    }

    if (httpRequestMessage.Content != null && !string.IsNullOrWhiteSpace(request.ContentType))
    {
      var preContentType = request.ContentType.Split(';', 2)[0].Trim();
      httpRequestMessage.Content.Headers.ContentType = new MediaTypeHeaderValue(
          string.IsNullOrWhiteSpace(preContentType) ? "application/json" : preContentType);
    }

    return httpRequestMessage;
  }

  private async Task<HttpClient> GetSignaturedWitLabClientAsync(string method, string path)
  {
    var url = new Uri($"{_witLabConfiguration.WITLAB_SERVER_HOST.TrimEnd('/')}/" +
                      $"{HttpUtility.UrlDecode(_witLabConfiguration.WITLAB_SERVER_SITE)}/rest.web.api/" +
                      $"{HttpUtility.UrlDecode(path)}");

    var httpClient = _httpClientFactory.CreateClient("WitLab");
    httpClient.BaseAddress = url;

    var timestamp = DateTime.UtcNow.ToString("o");

    var (ak, sk) = await _keyProvider.GetUserKeysAsync(_currentUser?.UserName, _currentUser?.DeptCode, _currentUser?.RoleCode);

    var sign = GetHmacSha256Signature(url.ToString(), method, ak, sk, timestamp);

    httpClient.DefaultRequestHeaders.TryAddWithoutValidation("SL-API-Auth", ak);
    httpClient.DefaultRequestHeaders.TryAddWithoutValidation("SL-API-Timestamp", timestamp);
    httpClient.DefaultRequestHeaders.TryAddWithoutValidation("SL-API-Signature", sign);

    return httpClient;
  }

  private static string GetHmacSha256Signature(string url, string method, string accessKey, string secretKey, string timestamp)
  {
    var signature = $"{url}{method}{accessKey}{timestamp}";
    using var hmac = new HMACSHA256(Encoding.UTF8.GetBytes(secretKey));
    var signatureBytes = hmac.ComputeHash(Encoding.UTF8.GetBytes(signature));
    return Convert.ToBase64String(signatureBytes);
  }
}
