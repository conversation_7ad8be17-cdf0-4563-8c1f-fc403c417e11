using Witlab.Platform.Core.Platform.Services;
using Witlab.Platform.UseCases.Platform.Notifications.DTOs;

namespace Witlab.Platform.UseCases.Platform.Notifications.Create;

/// <summary>
/// 根据模板创建通知命令处理器
/// </summary>
public class CreateNotificationFromTemplateHandler : IRequestHandler<CreateNotificationFromTemplateCommand, Result<CreateNotificationResponse>>
{
  private readonly INotificationService _notificationService;
  private readonly ILogger<CreateNotificationFromTemplateHandler> _logger;

  public CreateNotificationFromTemplateHandler(
    INotificationService notificationService,
    ILogger<CreateNotificationFromTemplateHandler> logger)
  {
    _notificationService = notificationService;
    _logger = logger;
  }

  public async Task<Result<CreateNotificationResponse>> Handle(CreateNotificationFromTemplateCommand request, CancellationToken cancellationToken)
  {
    try
    {
      _logger.LogInformation("开始根据模板创建通知，模板代码: {TemplateCode}，用户ID: {UserId}", 
        request.TemplateCode, request.UserId);

      var result = await _notificationService.CreateNotificationFromTemplateAsync(
        request.TemplateCode,
        request.UserId,
        request.Parameters,
        request.Priority,
        request.SourceType,
        request.SourceId,
        request.Data,
        request.ExpiresAt);

      if (!result.IsSuccess)
      {
        _logger.LogWarning("根据模板创建通知失败，模板代码: {TemplateCode}，用户ID: {UserId}，错误: {Errors}", 
          request.TemplateCode, request.UserId, string.Join(", ", result.Errors));
        return Result.Error(new ErrorList(result.Errors));
      }

      var notification = result.Value;
      var response = new CreateNotificationResponse(
        notification.Id,
        notification.Title,
        notification.Content);

      _logger.LogInformation("根据模板创建通知成功，通知ID: {NotificationId}，模板代码: {TemplateCode}，用户ID: {UserId}", 
        notification.Id, request.TemplateCode, request.UserId);

      return Result.Success(response);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "根据模板创建通知时发生异常，模板代码: {TemplateCode}，用户ID: {UserId}", 
        request.TemplateCode, request.UserId);
      return Result.Error("根据模板创建通知时发生系统错误");
    }
  }
}
