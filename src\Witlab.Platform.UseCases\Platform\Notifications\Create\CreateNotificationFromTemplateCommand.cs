using Witlab.Platform.Core.Platform.Enums;
using Witlab.Platform.UseCases.Platform.Notifications.DTOs;

namespace Witlab.Platform.UseCases.Platform.Notifications.Create;

/// <summary>
/// 根据模板创建通知命令
/// </summary>
public record CreateNotificationFromTemplateCommand(
    string TemplateCode,
    Guid UserId,
    Dictionary<string, string>? Parameters = null,
    NotificationPriority? Priority = null,
    string? SourceType = null,
    string? SourceId = null,
    string? Data = null,
    DateTime? ExpiresAt = null
) : IRequest<Result<CreateNotificationResponse>>;
