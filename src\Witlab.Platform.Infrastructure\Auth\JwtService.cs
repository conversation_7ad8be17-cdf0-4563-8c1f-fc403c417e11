﻿using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.IdentityModel.Tokens;
using Witlab.Platform.Core.Platform;
using Witlab.Platform.Infrastructure.Auth.Interfaces;
using Witlab.Platform.Infrastructure.Auth.Models;

namespace Witlab.Platform.Infrastructure.Auth;

/// <summary>
/// JWT服务实现
/// </summary>
public class JwtService : IJwtService
{
  private readonly JwtSettings _jwtSettings;
  private readonly IDistributedCache _cache;
  private readonly ILogger<JwtService> _logger;

  public JwtService(IOptions<JwtSettings> jwtSettings, IDistributedCache cache, ILogger<JwtService> logger)
  {
    _jwtSettings = jwtSettings.Value;
    _cache = cache;
    _logger = logger;
  }

  /// <inheritdoc />
  public string GenerateAccessToken(User user, IEnumerable<Role> roles, string? deptCode = null, string? roleCode = null)
  {
    try
    {
      _logger.LogDebug("开始为用户 {UserId} ({UserName}) 生成访问令牌", user.Id, user.UserName);

      var rolesList = roles.ToList();
      var roleNames = rolesList.Select(r => r.RoleCode).ToList();

      _logger.LogDebug("用户 {UserId} 拥有角色: {Roles}", user.Id, string.Join(", ", roleNames));

      var securityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_jwtSettings.Secret));
      var credentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256);

      var jti = Guid.NewGuid().ToString();
      var claims = new List<Claim>
            {
                new(JwtRegisteredClaimNames.Sub, user.Id.ToString()),
                new(JwtRegisteredClaimNames.Name, user.UserName),
                new(JwtRegisteredClaimNames.Jti, jti),
                new(ClaimTypes.NameIdentifier, user.Id.ToString()),
                new(ClaimTypes.Name, user.UserName)
            };

      // 添加角色声明
      foreach (var role in rolesList)
      {
        claims.Add(new Claim(ClaimTypes.Role, role.RoleCode));
      }

      if (deptCode != null)
      {
        claims.Add(new Claim($"Current{nameof(Dept.DeptCode)}", deptCode));
        _logger.LogInformation("用户 {UserId} ({UserName}) 当前登录：部门代码: {DeptCode}", user.Id, user.UserName, deptCode);
      }

      if (roleCode != null)
      {
        claims.Add(new Claim($"Current{nameof(Role.RoleCode)}", roleCode));
        _logger.LogInformation("用户 {UserId} ({UserName}) 当前登录：用户代码: {RoleCode}", user.Id, user.UserName, roleCode);
      }

      var expirationTime = DateTime.UtcNow.AddMinutes(_jwtSettings.AccessTokenExpirationMinutes);
      var token = new JwtSecurityToken(
          issuer: _jwtSettings.Issuer,
          audience: _jwtSettings.Audience,
          claims: claims,
          expires: expirationTime,
          signingCredentials: credentials
      );

      var tokenString = new JwtSecurityTokenHandler().WriteToken(token);

      _logger.LogInformation("成功为用户 {UserId} ({UserName}) 生成访问令牌，JTI: {Jti}，过期时间: {ExpirationTime}",
          user.Id, user.UserName, jti, expirationTime);

      return tokenString;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "为用户 {UserId} ({UserName}) 生成访问令牌时发生错误", user.Id, user.UserName);
      throw;
    }
  }

  /// <inheritdoc />
  public string GenerateRefreshToken()
  {
    try
    {
      _logger.LogDebug("开始生成刷新令牌");

      var randomNumber = new byte[32];
      using var rng = RandomNumberGenerator.Create();
      rng.GetBytes(randomNumber);
      var refreshToken = Convert.ToBase64String(randomNumber);

      _logger.LogDebug("成功生成刷新令牌，长度: {TokenLength}", refreshToken.Length);

      return refreshToken;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "生成刷新令牌时发生错误");
      throw;
    }
  }

  /// <inheritdoc />
  public bool ValidateAccessToken(string token)
  {
    if (string.IsNullOrEmpty(token))
    {
      _logger.LogWarning("令牌验证失败：令牌为空或null");
      return false;
    }

    _logger.LogDebug("开始验证访问令牌，令牌长度: {TokenLength}", token.Length);

    var tokenHandler = new JwtSecurityTokenHandler();
    var key = Encoding.UTF8.GetBytes(_jwtSettings.Secret);

    try
    {
      tokenHandler.ValidateToken(token, new TokenValidationParameters
      {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(key),
        ValidateIssuer = true,
        ValidIssuer = _jwtSettings.Issuer,
        ValidateAudience = true,
        ValidAudience = _jwtSettings.Audience,
        ValidateLifetime = true,
        ClockSkew = TimeSpan.Zero
      }, out var validatedToken);

      var jwtToken = (JwtSecurityToken)validatedToken;
      var userId = jwtToken.Claims.FirstOrDefault(x => x.Type == JwtRegisteredClaimNames.Sub)?.Value;
      var jti = jwtToken.Claims.FirstOrDefault(x => x.Type == JwtRegisteredClaimNames.Jti)?.Value;

      _logger.LogDebug("令牌验证成功，用户ID: {UserId}，JTI: {Jti}，过期时间: {ExpirationTime}",
          userId, jti, jwtToken.ValidTo);

      return true;
    }
    catch (SecurityTokenExpiredException ex)
    {
      _logger.LogInformation("令牌验证失败：令牌已过期 - {Message}", ex.Message);
      return false;
    }
    catch (SecurityTokenInvalidSignatureException ex)
    {
      _logger.LogWarning("令牌验证失败：签名无效 - {Message}", ex.Message);
      return false;
    }
    catch (SecurityTokenInvalidIssuerException ex)
    {
      _logger.LogWarning("令牌验证失败：发行者无效 - {Message}", ex.Message);
      return false;
    }
    catch (SecurityTokenInvalidAudienceException ex)
    {
      _logger.LogWarning("令牌验证失败：受众无效 - {Message}", ex.Message);
      return false;
    }
    catch (SecurityTokenException ex)
    {
      _logger.LogWarning("令牌验证失败：{ExceptionType} - {Message}", ex.GetType().Name, ex.Message);
      return false;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "令牌验证时发生意外错误");
      return false;
    }
  }

  /// <inheritdoc />
  public Guid? GetUserIdFromToken(string token)
  {
    if (string.IsNullOrEmpty(token))
    {
      _logger.LogWarning("从令牌获取用户ID失败：令牌为空或null");
      return null;
    }

    _logger.LogDebug("开始从令牌中提取用户ID");

    var tokenHandler = new JwtSecurityTokenHandler();
    var key = Encoding.UTF8.GetBytes(_jwtSettings.Secret);

    try
    {
      tokenHandler.ValidateToken(token, new TokenValidationParameters
      {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(key),
        ValidateIssuer = true,
        ValidIssuer = _jwtSettings.Issuer,
        ValidateAudience = true,
        ValidAudience = _jwtSettings.Audience,
        ValidateLifetime = false, // 允许过期的令牌，只提取用户ID
        ClockSkew = TimeSpan.Zero
      }, out var validatedToken);

      var jwtToken = (JwtSecurityToken)validatedToken;
      var userIdClaim = jwtToken.Claims.FirstOrDefault(x => x.Type == JwtRegisteredClaimNames.Sub);

      if (userIdClaim == null)
      {
        _logger.LogWarning("令牌中未找到用户ID声明 (Sub)");
        return null;
      }

      if (!Guid.TryParse(userIdClaim.Value, out var userId))
      {
        _logger.LogWarning("令牌中的用户ID格式无效: {UserId}", userIdClaim.Value);
        return null;
      }

      _logger.LogDebug("成功从令牌中提取用户ID: {UserId}", userId);
      return userId;
    }
    catch (SecurityTokenExpiredException ex)
    {
      _logger.LogDebug("令牌已过期，但仍尝试提取用户ID: {Message}", ex.Message);

      // 对于过期的令牌，尝试直接读取而不验证
      try
      {
        var jwtToken = tokenHandler.ReadJwtToken(token);
        var userIdClaim = jwtToken.Claims.FirstOrDefault(x => x.Type == JwtRegisteredClaimNames.Sub);

        if (userIdClaim != null && Guid.TryParse(userIdClaim.Value, out var userId))
        {
          _logger.LogDebug("从过期令牌中成功提取用户ID: {UserId}", userId);
          return userId;
        }
      }
      catch (Exception readEx)
      {
        _logger.LogWarning(readEx, "读取过期令牌时发生错误");
      }

      return null;
    }
    catch (SecurityTokenException ex)
    {
      _logger.LogWarning("从令牌提取用户ID失败：{ExceptionType} - {Message}", ex.GetType().Name, ex.Message);
      return null;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "从令牌提取用户ID时发生意外错误");
      return null;
    }
  }

  /// <inheritdoc />
  public string? GetUserNameFromToken(string token)
  {
    if (string.IsNullOrEmpty(token))
    {
      _logger.LogWarning("从令牌获取用户名失败：令牌为空或null");
      return null;
    }

    _logger.LogDebug("开始从令牌中提取用户名");

    var tokenHandler = new JwtSecurityTokenHandler();
    var key = Encoding.UTF8.GetBytes(_jwtSettings.Secret);

    try
    {
      tokenHandler.ValidateToken(token, new TokenValidationParameters
      {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(key),
        ValidateIssuer = true,
        ValidIssuer = _jwtSettings.Issuer,
        ValidateAudience = true,
        ValidAudience = _jwtSettings.Audience,
        ValidateLifetime = false, // 允许过期的令牌，只提取用户名
        ClockSkew = TimeSpan.Zero
      }, out var validatedToken);

      var jwtToken = (JwtSecurityToken)validatedToken;
      var userNameClaim = jwtToken.Claims.FirstOrDefault(x => x.Type == JwtRegisteredClaimNames.Name);

      if (userNameClaim == null)
      {
        _logger.LogWarning("令牌中未找到用户名声明 (Name)");
        return null;
      }

      _logger.LogDebug("成功从令牌中提取用户名: {UserName}", userNameClaim.Value);
      return userNameClaim.Value;
    }
    catch (SecurityTokenExpiredException ex)
    {
      _logger.LogDebug("令牌已过期，但仍尝试提取用户名: {Message}", ex.Message);

      // 对于过期的令牌，尝试直接读取而不验证
      try
      {
        var jwtToken = tokenHandler.ReadJwtToken(token);
        var userNameClaim = jwtToken.Claims.FirstOrDefault(x => x.Type == JwtRegisteredClaimNames.Name);

        if (userNameClaim != null)
        {
          _logger.LogDebug("从过期令牌中成功提取用户名: {UserName}", userNameClaim.Value);
          return userNameClaim.Value;
        }
      }
      catch (Exception readEx)
      {
        _logger.LogWarning(readEx, "读取过期令牌时发生错误");
      }

      return null;
    }
    catch (SecurityTokenException ex)
    {
      _logger.LogWarning("从令牌提取用户名失败：{ExceptionType} - {Message}", ex.GetType().Name, ex.Message);
      return null;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "从令牌提取用户名时发生意外错误");
      return null;
    }
  }

  /// <inheritdoc />
  public string? GetJtiFromToken(string token)
  {
    if (string.IsNullOrEmpty(token))
    {
      _logger.LogWarning("从令牌获取JTI失败：令牌为空或null");
      return null;
    }

    _logger.LogDebug("开始从令牌中提取JTI");

    var tokenHandler = new JwtSecurityTokenHandler();
    var key = Encoding.UTF8.GetBytes(_jwtSettings.Secret);

    try
    {
      tokenHandler.ValidateToken(token, new TokenValidationParameters
      {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(key),
        ValidateIssuer = true,
        ValidIssuer = _jwtSettings.Issuer,
        ValidateAudience = true,
        ValidAudience = _jwtSettings.Audience,
        ValidateLifetime = false, // 允许过期的令牌，只提取JTI
        ClockSkew = TimeSpan.Zero
      }, out var validatedToken);

      var jwtToken = (JwtSecurityToken)validatedToken;
      var jtiClaim = jwtToken.Claims.FirstOrDefault(x => x.Type == JwtRegisteredClaimNames.Jti);

      if (jtiClaim == null)
      {
        _logger.LogWarning("令牌中未找到JTI声明");
        return null;
      }

      _logger.LogDebug("成功从令牌中提取JTI: {Jti}", jtiClaim.Value);
      return jtiClaim.Value;
    }
    catch (SecurityTokenExpiredException ex)
    {
      _logger.LogDebug("令牌已过期，但仍尝试提取JTI: {Message}", ex.Message);

      // 对于过期的令牌，尝试直接读取而不验证
      try
      {
        var jwtToken = tokenHandler.ReadJwtToken(token);
        var jtiClaim = jwtToken.Claims.FirstOrDefault(x => x.Type == JwtRegisteredClaimNames.Jti);

        if (jtiClaim != null)
        {
          _logger.LogDebug("从过期令牌中成功提取JTI: {Jti}", jtiClaim.Value);
          return jtiClaim.Value;
        }
      }
      catch (Exception readEx)
      {
        _logger.LogWarning(readEx, "读取过期令牌时发生错误");
      }

      return null;
    }
    catch (SecurityTokenException ex)
    {
      _logger.LogWarning("从令牌提取JTI失败：{ExceptionType} - {Message}", ex.GetType().Name, ex.Message);
      return null;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "从令牌提取JTI时发生意外错误");
      return null;
    }
  }

  /// <inheritdoc />
  public IEnumerable<string> GetRolesFromToken(string token)
  {
    if (string.IsNullOrEmpty(token))
    {
      _logger.LogWarning("从令牌获取角色失败：令牌为空或null");
      return [];
    }

    _logger.LogDebug("开始从令牌中提取角色信息");

    var tokenHandler = new JwtSecurityTokenHandler();
    var key = Encoding.UTF8.GetBytes(_jwtSettings.Secret);

    try
    {
      tokenHandler.ValidateToken(token, new TokenValidationParameters
      {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(key),
        ValidateIssuer = true,
        ValidIssuer = _jwtSettings.Issuer,
        ValidateAudience = true,
        ValidAudience = _jwtSettings.Audience,
        ValidateLifetime = false, // 允许过期的令牌，只提取角色
        ClockSkew = TimeSpan.Zero
      }, out var validatedToken);

      var jwtToken = (JwtSecurityToken)validatedToken;
      var roles = jwtToken.Claims
          .Where(x => x.Type == ClaimTypes.Role)
          .Select(x => x.Value)
          .ToList();

      _logger.LogDebug("成功从令牌中提取角色信息，角色数量: {RoleCount}，角色: {Roles}",
          roles.Count, string.Join(", ", roles));

      return roles;
    }
    catch (SecurityTokenExpiredException ex)
    {
      _logger.LogDebug("令牌已过期，但仍尝试提取角色信息: {Message}", ex.Message);

      // 对于过期的令牌，尝试直接读取而不验证
      try
      {
        var jwtToken = tokenHandler.ReadJwtToken(token);
        var roles = jwtToken.Claims
            .Where(x => x.Type == ClaimTypes.Role)
            .Select(x => x.Value)
            .ToList();

        _logger.LogDebug("从过期令牌中成功提取角色信息，角色数量: {RoleCount}，角色: {Roles}",
            roles.Count, string.Join(", ", roles));

        return roles;
      }
      catch (Exception readEx)
      {
        _logger.LogWarning(readEx, "读取过期令牌时发生错误");
      }

      return [];
    }
    catch (SecurityTokenException ex)
    {
      _logger.LogWarning("从令牌提取角色失败：{ExceptionType} - {Message}", ex.GetType().Name, ex.Message);
      return [];
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "从令牌提取角色时发生意外错误");
      return [];
    }
  }

  /// <inheritdoc />
  public string? GetDeptCodeFromToken(string token)
  {
    if (string.IsNullOrEmpty(token))
    {
      _logger.LogWarning("从令牌获取当前部门编码失败：令牌为空或null");
      return null;
    }
    _logger.LogDebug("开始从令牌中提取当前部门编码");
    var tokenHandler = new JwtSecurityTokenHandler();
    var key = Encoding.UTF8.GetBytes(_jwtSettings.Secret);
    try
    {
      tokenHandler.ValidateToken(token, new TokenValidationParameters
      {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(key),
        ValidateIssuer = true,
        ValidIssuer = _jwtSettings.Issuer,
        ValidateAudience = true,
        ValidAudience = _jwtSettings.Audience,
        ValidateLifetime = false, // 允许过期的令牌，只提取部门编码
        ClockSkew = TimeSpan.Zero
      }, out var validatedToken);
      var jwtToken = (JwtSecurityToken)validatedToken;
      var deptCodeClaim = jwtToken.Claims.FirstOrDefault(x => x.Type == $"Current{nameof(Dept.DeptCode)}");
      if (deptCodeClaim == null)
      {
        _logger.LogWarning("令牌中未找到当前部门编码声明 (DeptCode)");
        return null;
      }
      _logger.LogDebug("成功从令牌中提取当前部门编码: {DeptCode}", deptCodeClaim.Value);
      return deptCodeClaim.Value;
    }
    catch (SecurityTokenExpiredException ex)
    {
      _logger.LogDebug("令牌已过期，但仍尝试提取当前部门编码: {Message}", ex.Message);
      // 对于过期的令牌，尝试直接读取而不验证
      try
      {
        var jwtToken = tokenHandler.ReadJwtToken(token);
        var deptCodeClaim = jwtToken.Claims.FirstOrDefault(x => x.Type == $"Current{nameof(Dept.DeptCode)}");
        if (deptCodeClaim != null)
        {
          _logger.LogDebug("从过期令牌中成功提取当前部门编码: {DeptCode}", deptCodeClaim.Value);
          return deptCodeClaim.Value;
        }
      }
      catch (Exception readEx)
      {
        _logger.LogWarning(readEx, "读取过期令牌时发生错误");
      }
      return null;
    }
  }

  public string? GetRoleCodeFromToken(string token)
  {
    if (string.IsNullOrEmpty(token))
    {
      _logger.LogWarning("从令牌获取当前角色编码失败：令牌为空或null");
      return null;
    }
    _logger.LogDebug("开始从令牌中提取当前角色编码");
    var tokenHandler = new JwtSecurityTokenHandler();
    var key = Encoding.UTF8.GetBytes(_jwtSettings.Secret);
    try
    {
      tokenHandler.ValidateToken(token, new TokenValidationParameters
      {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(key),
        ValidateIssuer = true,
        ValidIssuer = _jwtSettings.Issuer,
        ValidateAudience = true,
        ValidAudience = _jwtSettings.Audience,
        ValidateLifetime = false, // 允许过期的令牌，只提取角色编码
        ClockSkew = TimeSpan.Zero
      }, out var validatedToken);
      var jwtToken = (JwtSecurityToken)validatedToken;
      var roleCodeClaim = jwtToken.Claims.FirstOrDefault(x => x.Type == $"Current{nameof(Role.RoleCode)}");
      if (roleCodeClaim == null)
      {
        _logger.LogWarning("令牌中未找到当前角色编码声明 (RoleCode)");
        return null;
      }
      _logger.LogDebug("成功从令牌中提取当前角色编码: {RoleCode}", roleCodeClaim.Value);
      return roleCodeClaim.Value;
    }
    catch (SecurityTokenExpiredException ex)
    {
      _logger.LogDebug("令牌已过期，但仍尝试提取当前角色编码: {Message}", ex.Message);
      // 对于过期���令牌，尝试直接读取而不验证
      try
      {
        var jwtToken = tokenHandler.ReadJwtToken(token);
        var roleCodeClaim = jwtToken.Claims.FirstOrDefault(x => x.Type == $"Current{nameof(Role.RoleCode)}");
        if (roleCodeClaim != null)
        {
          _logger.LogDebug("从过期令牌中成功提取当前角色编码: {RoleCode}", roleCodeClaim.Value);
          return roleCodeClaim.Value;
        }
      }
      catch (Exception readEx)
      {
        _logger.LogWarning(readEx, "读取过期令牌时发生错误");
      }
      return null;
    }
  }

  /// <inheritdoc />
  public async Task<bool> AddToBlacklistAsync(string token)
  {
    if (string.IsNullOrEmpty(token))
    {
      _logger.LogWarning("添加令牌到黑名单失败：令牌为空或null");
      return false;
    }

    _logger.LogDebug("开始将令牌添加到黑名单");

    try
    {
      var tokenHandler = new JwtSecurityTokenHandler();
      var jwtToken = tokenHandler.ReadJwtToken(token);
      var expiration = jwtToken.ValidTo;
      var timeToLive = expiration - DateTime.UtcNow;

      // 获取令牌信息用于日志
      var userId = jwtToken.Claims.FirstOrDefault(x => x.Type == JwtRegisteredClaimNames.Sub)?.Value;
      var jti = jwtToken.Claims.FirstOrDefault(x => x.Type == JwtRegisteredClaimNames.Jti)?.Value;

      if (timeToLive <= TimeSpan.Zero)
      {
        _logger.LogInformation("令牌已过期，无需加入黑名单。用户ID: {UserId}，JTI: {Jti}，过期时间: {ExpirationTime}",
            userId, jti, expiration);
        return true; // 令牌已过期，无需加入黑名单
      }

      var cacheKey = $"blacklist_{token}";
      await _cache.SetStringAsync(
          cacheKey,
          "revoked",
          new DistributedCacheEntryOptions
          {
            AbsoluteExpirationRelativeToNow = timeToLive
          });

      _logger.LogInformation("成功将令牌添加到黑名单。用户ID: {UserId}，JTI: {Jti}，缓存过期时间: {TimeToLive}",
          userId, jti, timeToLive);

      return true;
    }
    catch (ArgumentException ex)
    {
      _logger.LogWarning("添加令牌到黑名单失败：令牌格式无效 - {Message}", ex.Message);
      return false;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "添加令牌到黑名单时发生意外错误");
      return false;
    }
  }

  /// <inheritdoc />
  public async Task<bool> IsInBlacklistAsync(string token)
  {
    if (string.IsNullOrEmpty(token))
    {
      _logger.LogWarning("检查令牌黑名单状态失败：令牌为空或null");
      return false;
    }

    _logger.LogDebug("开始检查令牌是否在黑名单中");

    try
    {
      var cacheKey = $"blacklist_{token}";
      var value = await _cache.GetStringAsync(cacheKey);
      var isBlacklisted = !string.IsNullOrEmpty(value);

      if (isBlacklisted)
      {
        // 尝试获取令牌信息用于日志
        try
        {
          var tokenHandler = new JwtSecurityTokenHandler();
          var jwtToken = tokenHandler.ReadJwtToken(token);
          var userId = jwtToken.Claims.FirstOrDefault(x => x.Type == JwtRegisteredClaimNames.Sub)?.Value;
          var jti = jwtToken.Claims.FirstOrDefault(x => x.Type == JwtRegisteredClaimNames.Jti)?.Value;

          _logger.LogInformation("令牌在黑名单中。用户ID: {UserId}，JTI: {Jti}", userId, jti);
        }
        catch
        {
          _logger.LogInformation("令牌在黑名单中（无法解析令牌信息）");
        }
      }
      else
      {
        _logger.LogDebug("令牌不在黑名单中");
      }

      return isBlacklisted;
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "检查令牌黑名单状态时发生错误");
      return false;
    }
  }
}
