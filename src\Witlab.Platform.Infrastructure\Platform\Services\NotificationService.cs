using Witlab.Platform.Core.Platform;
using Witlab.Platform.Core.Platform.Enums;
using Witlab.Platform.Core.Platform.Services;

namespace Witlab.Platform.Infrastructure.Platform.Services;

/// <summary>
/// 通知领域服务实现
/// </summary>
public class NotificationService : INotificationService
{
  private readonly IRepository<Notification> _notificationRepository;
  private readonly INotificationTemplateService _templateService;
  private readonly ILogger<NotificationService> _logger;

  public NotificationService(
    IRepository<Notification> notificationRepository,
    INotificationTemplateService templateService,
    ILogger<NotificationService> logger)
  {
    _notificationRepository = notificationRepository;
    _templateService = templateService;
    _logger = logger;
  }

  public async Task<Result<Notification>> CreateNotificationAsync(
    Guid userId, 
    string title, 
    string content, 
    NotificationType type, 
    NotificationCategory category, 
    NotificationPriority priority = null, 
    string sourceType = null, 
    string sourceId = null, 
    string data = null, 
    DateTime? expiresAt = null)
  {
    try
    {
      var notification = new Notification(
        userId, 
        title, 
        content, 
        type, 
        category, 
        priority ?? NotificationPriority.Normal, 
        sourceType, 
        sourceId, 
        data, 
        expiresAt);

      await _notificationRepository.AddAsync(notification);
      await _notificationRepository.SaveChangesAsync();

      _logger.LogInformation("通知创建成功，通知ID: {NotificationId}，用户ID: {UserId}", 
        notification.Id, userId);

      return Result.Success(notification);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "创建通知时发生异常，用户ID: {UserId}", userId);
      return Result.Error("创建通知失败");
    }
  }

  public async Task<Result<Notification>> CreateNotificationFromTemplateAsync(
    string templateCode, 
    Guid userId, 
    Dictionary<string, string> parameters = null, 
    NotificationPriority priority = null, 
    string sourceType = null, 
    string sourceId = null, 
    string data = null, 
    DateTime? expiresAt = null)
  {
    try
    {
      var templateResult = await _templateService.GetTemplateByCodeAsync(templateCode);
      if (!templateResult.IsSuccess)
      {
        return Result.Error($"通知模板不存在: {templateCode}");
      }

      var template = templateResult.Value;
      if (!template.IsActive)
      {
        return Result.Error($"通知模板已禁用: {templateCode}");
      }

      var (title, content) = template.GenerateContent(parameters);

      return await CreateNotificationAsync(
        userId,
        title,
        content,
        template.Type,
        template.Category,
        priority ?? template.Priority,
        sourceType,
        sourceId,
        data,
        expiresAt);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "根据模板创建通知时发生异常，模板代码: {TemplateCode}，用户ID: {UserId}", 
        templateCode, userId);
      return Result.Error("根据模板创建通知失败");
    }
  }

  public async Task<Result> MarkAsReadAsync(Guid notificationId, Guid userId)
  {
    try
    {
      var notification = await _notificationRepository.GetByIdAsync(notificationId);
      if (notification == null)
      {
        return Result.Error("通知不存在");
      }

      if (notification.UserId != userId)
      {
        return Result.Error("无权限访问此通知");
      }

      notification.MarkAsRead();
      await _notificationRepository.UpdateAsync(notification);
      await _notificationRepository.SaveChangesAsync();

      _logger.LogInformation("通知标记为已读成功，通知ID: {NotificationId}，用户ID: {UserId}", 
        notificationId, userId);

      return Result.Success();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "标记通知为已读时发生异常，通知ID: {NotificationId}，用户ID: {UserId}", 
        notificationId, userId);
      return Result.Error("标记通知为已读失败");
    }
  }

  public async Task<Result> MarkAllAsReadAsync(Guid userId)
  {
    try
    {
      var spec = new NotificationsByUserAndStatusSpec(userId, NotificationStatus.Unread);
      var unreadNotifications = await _notificationRepository.ListAsync(spec);

      foreach (var notification in unreadNotifications)
      {
        notification.MarkAsRead();
      }

      await _notificationRepository.UpdateRangeAsync(unreadNotifications);
      await _notificationRepository.SaveChangesAsync();

      _logger.LogInformation("用户所有通知标记为已读成功，用户ID: {UserId}，数量: {Count}", 
        userId, unreadNotifications.Count);

      return Result.Success();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "标记所有通知为已读时发生异常，用户ID: {UserId}", userId);
      return Result.Error("标记所有通知为已读失败");
    }
  }

  public async Task<Result> ArchiveNotificationAsync(Guid notificationId, Guid userId)
  {
    try
    {
      var notification = await _notificationRepository.GetByIdAsync(notificationId);
      if (notification == null)
      {
        return Result.Error("通知不存在");
      }

      if (notification.UserId != userId)
      {
        return Result.Error("无权限访问此通知");
      }

      notification.Archive();
      await _notificationRepository.UpdateAsync(notification);
      await _notificationRepository.SaveChangesAsync();

      _logger.LogInformation("通知归档成功，通知ID: {NotificationId}，用户ID: {UserId}", 
        notificationId, userId);

      return Result.Success();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "归档通知时发生异常，通知ID: {NotificationId}，用户ID: {UserId}", 
        notificationId, userId);
      return Result.Error("归档通知失败");
    }
  }

  public async Task<Result<int>> GetUnreadCountAsync(Guid userId)
  {
    try
    {
      var spec = new NotificationsByUserAndStatusSpec(userId, NotificationStatus.Unread);
      var count = await _notificationRepository.CountAsync(spec);

      return Result.Success(count);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取未读通知数量时发生异常，用户ID: {UserId}", userId);
      return Result.Error("获取未读通知数量失败");
    }
  }

  public async Task<Result<int>> DeleteExpiredNotificationsAsync()
  {
    try
    {
      var spec = new ExpiredNotificationsSpec();
      var expiredNotifications = await _notificationRepository.ListAsync(spec);

      await _notificationRepository.DeleteRangeAsync(expiredNotifications);
      await _notificationRepository.SaveChangesAsync();

      _logger.LogInformation("删除过期通知成功，数量: {Count}", expiredNotifications.Count);

      return Result.Success(expiredNotifications.Count);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "删除过期通知时发生异常");
      return Result.Error("删除过期通知失败");
    }
  }

  public async Task<Result<IEnumerable<Notification>>> GetUserNotificationsAsync(
    Guid userId, 
    NotificationStatus? status = null, 
    NotificationCategory? category = null, 
    int skip = 0, 
    int take = 20)
  {
    try
    {
      var spec = new NotificationsByUserSpec(userId, status, category, skip, take);
      var notifications = await _notificationRepository.ListAsync(spec);

      return Result.Success(notifications.AsEnumerable());
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取用户通知列表时发生异常，用户ID: {UserId}", userId);
      return Result.Error("获取用户通知列表失败");
    }
  }

  public async Task<Result<Notification>> GetNotificationByIdAsync(Guid notificationId, Guid userId)
  {
    try
    {
      var notification = await _notificationRepository.GetByIdAsync(notificationId);
      if (notification == null)
      {
        return Result.Error("通知不存在");
      }

      if (notification.UserId != userId)
      {
        return Result.Error("无权限访问此通知");
      }

      return Result.Success(notification);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "获取通知详情时发生异常，通知ID: {NotificationId}，用户ID: {UserId}", 
        notificationId, userId);
      return Result.Error("获取通知详情失败");
    }
  }

  public async Task<Result> DeleteNotificationAsync(Guid notificationId, Guid userId)
  {
    try
    {
      var notification = await _notificationRepository.GetByIdAsync(notificationId);
      if (notification == null)
      {
        return Result.Error("通知不存在");
      }

      if (notification.UserId != userId)
      {
        return Result.Error("无权限访问此通知");
      }

      await _notificationRepository.DeleteAsync(notification);
      await _notificationRepository.SaveChangesAsync();

      _logger.LogInformation("通知删除成功，通知ID: {NotificationId}，用户ID: {UserId}", 
        notificationId, userId);

      return Result.Success();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "删除通知时发生异常，通知ID: {NotificationId}，用户ID: {UserId}", 
        notificationId, userId);
      return Result.Error("删除通知失败");
    }
  }
}
