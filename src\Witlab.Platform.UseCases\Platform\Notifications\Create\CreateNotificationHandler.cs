using Witlab.Platform.Core.Platform.Services;
using Witlab.Platform.UseCases.Platform.Notifications.DTOs;

namespace Witlab.Platform.UseCases.Platform.Notifications.Create;

/// <summary>
/// 创建通知命令处理器
/// </summary>
public class CreateNotificationHandler : IRequestHandler<CreateNotificationCommand, Result<CreateNotificationResponse>>
{
  private readonly INotificationService _notificationService;
  private readonly ILogger<CreateNotificationHandler> _logger;

  public CreateNotificationHandler(
    INotificationService notificationService,
    ILogger<CreateNotificationHandler> logger)
  {
    _notificationService = notificationService;
    _logger = logger;
  }

  public async Task<Result<CreateNotificationResponse>> Handle(CreateNotificationCommand request, CancellationToken cancellationToken)
  {
    try
    {
      _logger.LogInformation("开始创建通知，用户ID: {UserId}，标题: {Title}", request.UserId, request.Title);

      var result = await _notificationService.CreateNotificationAsync(
        request.UserId,
        request.Title,
        request.Content,
        request.Type,
        request.Category,
        request.Priority,
        request.SourceType,
        request.SourceId,
        request.Data,
        request.ExpiresAt);

      if (!result.IsSuccess)
      {
        _logger.LogWarning("创建通知失败，用户ID: {UserId}，错误: {Errors}", 
          request.UserId, string.Join(", ", result.Errors));
        return Result.Error(new ErrorList(result.Errors));
      }

      var notification = result.Value;
      var response = new CreateNotificationResponse(
        notification.Id,
        notification.Title,
        notification.Content);

      _logger.LogInformation("通知创建成功，通知ID: {NotificationId}，用户ID: {UserId}", 
        notification.Id, request.UserId);

      return Result.Success(response);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "创建通知时发生异常，用户ID: {UserId}", request.UserId);
      return Result.Error("创建通知时发生系统错误");
    }
  }
}
