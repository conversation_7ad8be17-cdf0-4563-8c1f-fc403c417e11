using Witlab.Platform.Core.Platform.Events;
using Witlab.Platform.Infrastructure.Notifications.Services;

namespace Witlab.Platform.Infrastructure.Notifications.EventHandlers;

/// <summary>
/// 通知已读事件处理器
/// </summary>
public class NotificationReadHandler : INotificationHandler<NotificationReadEvent>
{
  private readonly IRealtimeNotificationService _realtimeNotificationService;
  private readonly INotificationCacheService _cacheService;
  private readonly ILogger<NotificationReadHandler> _logger;

  public NotificationReadHandler(
    IRealtimeNotificationService realtimeNotificationService,
    INotificationCacheService cacheService,
    ILogger<NotificationReadHandler> logger)
  {
    _realtimeNotificationService = realtimeNotificationService;
    _cacheService = cacheService;
    _logger = logger;
  }

  public async Task Handle(NotificationReadEvent notification, CancellationToken cancellationToken)
  {
    try
    {
      _logger.LogInformation("开始处理通知已读事件，通知ID: {NotificationId}，用户ID: {UserId}", 
        notification.NotificationId, notification.UserId);

      // 更新未读数量缓存
      await _cacheService.DecrementUnreadCountAsync(notification.UserId);

      // 发送通知状态更新
      await _realtimeNotificationService.SendNotificationStatusUpdateAsync(
        notification.UserId, notification.NotificationId, "Read");

      // 发送未读数量更新
      var unreadCount = await _cacheService.GetUnreadCountAsync(notification.UserId);
      if (unreadCount.HasValue)
      {
        await _realtimeNotificationService.SendUnreadCountUpdateAsync(
          notification.UserId, unreadCount.Value);
      }

      _logger.LogInformation("通知已读事件处理完成，通知ID: {NotificationId}，用户ID: {UserId}", 
        notification.NotificationId, notification.UserId);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "处理通知已读事件时发生异常，通知ID: {NotificationId}，用户ID: {UserId}", 
        notification.NotificationId, notification.UserId);
    }
  }
}
