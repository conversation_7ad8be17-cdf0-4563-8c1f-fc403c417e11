namespace Witlab.Platform.Core.Platform.Events;

/// <summary>
/// 通知已读事件
/// </summary>
public class NotificationReadEvent : DomainEventBase
{
  public NotificationReadEvent(Guid notificationId, Guid userId)
  {
    NotificationId = notificationId;
    UserId = userId;
  }

  /// <summary>
  /// 通知ID
  /// </summary>
  public Guid NotificationId { get; }

  /// <summary>
  /// 用户ID
  /// </summary>
  public Guid UserId { get; }
}
