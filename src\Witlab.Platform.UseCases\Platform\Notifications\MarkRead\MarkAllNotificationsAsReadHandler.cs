using Witlab.Platform.Core.Platform.Services;

namespace Witlab.Platform.UseCases.Platform.Notifications.MarkRead;

/// <summary>
/// 标记所有通知为已读命令处理器
/// </summary>
public class MarkAllNotificationsAsReadHandler : IRequestHandler<MarkAllNotificationsAsReadCommand, Result>
{
  private readonly INotificationService _notificationService;
  private readonly ILogger<MarkAllNotificationsAsReadHandler> _logger;

  public MarkAllNotificationsAsReadHandler(
    INotificationService notificationService,
    ILogger<MarkAllNotificationsAsReadHandler> logger)
  {
    _notificationService = notificationService;
    _logger = logger;
  }

  public async Task<Result> Handle(MarkAllNotificationsAsReadCommand request, CancellationToken cancellationToken)
  {
    try
    {
      _logger.LogInformation("开始标记所有通知为已读，用户ID: {UserId}", request.UserId);

      var result = await _notificationService.MarkAllAsReadAsync(request.UserId);

      if (!result.IsSuccess)
      {
        _logger.LogWarning("标记所有通知为已读失败，用户ID: {UserId}，错误: {Errors}", 
          request.UserId, string.Join(", ", result.Errors));
        return Result.Error(new ErrorList(result.Errors));
      }

      _logger.LogInformation("标记所有通知为已读成功，用户ID: {UserId}", request.UserId);

      return Result.Success();
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "标记所有通知为已读时发生异常，用户ID: {UserId}", request.UserId);
      return Result.Error("标记所有通知为已读时发生系统错误");
    }
  }
}
