﻿using Microsoft.EntityFrameworkCore.Diagnostics;
using Witlab.Platform.Core.ContributorAggregate.Interfaces;
using Witlab.Platform.Core.ContributorAggregate.Services;
using Witlab.Platform.Core.Platform.Interfaces;
using Witlab.Platform.Core.Platform.Services;
using Witlab.Platform.Core.WitLab.Interfaces;
using Witlab.Platform.Core.WitLab.Services;
using Witlab.Platform.Infrastructure.Auth;
using Witlab.Platform.Infrastructure.Auth.Interfaces;
using Witlab.Platform.Infrastructure.Auth.Models;
using Witlab.Platform.Infrastructure.Data;
using Witlab.Platform.Infrastructure.Data.Interceptors;
using Witlab.Platform.Infrastructure.Data.Queries;
using Witlab.Platform.Infrastructure.Platform.Queries;
using Witlab.Platform.UseCases.Contributors.List;
using Witlab.Platform.UseCases.Platform.Depts.List;
using Witlab.Platform.UseCases.Platform.Menus.List;
using Witlab.Platform.UseCases.Platform.Permissions.List;
using Witlab.Platform.UseCases.Platform.Roles.List;
using Witlab.Platform.UseCases.Platform.Users.List;


namespace Witlab.Platform.Infrastructure;
public static class InfrastructureServiceExtensions
{
  public static IServiceCollection AddInfrastructureServices(
    this IServiceCollection services,
    ConfigurationManager config,
    ILogger logger)
  {
    services.AddScoped<ISaveChangesInterceptor, AuditableEntityInterceptor>();

    services.AddDbContext<AppDbContext>((sp, options) =>
    {
      options.AddInterceptors(sp.GetServices<ISaveChangesInterceptor>());
      var provider = config.GetValue<string>("DatabaseProvider");
      switch (provider?.ToLower())
      {
        //case "oracle":
        //  var oracleConnectionString = config.GetConnectionString("OracleConnection");
        //  Guard.Against.Null(oracleConnectionString, message: "Connection string 'OracleConnection' not found.");
        //  options.UseOracle(oracleConnectionString, option =>
        //  {
        //    option.UseOracleSQLCompatibility(OracleSQLCompatibility.DatabaseVersion19);
        //  });
        //  break;
        case "sqlserver":
          var defaultConnectionString = config.GetConnectionString("DefaultConnection");
          Guard.Against.Null(defaultConnectionString, message: "Connection string 'DefaultConnection' not found.");
          options.UseSqlServer(defaultConnectionString);
          break;

        default:
          string? connectionString = config.GetConnectionString("SqliteConnection");
          Guard.Against.Null(connectionString);
          options.UseSqlite(connectionString);
          break;
      }
      //options.AddInterceptors(sp.GetRequiredService<SecondLevelCacheInterceptor>());
    });
    services.AddScoped<IUnitOfWork>(p => p.GetRequiredService<AppDbContext>());
    services.AddScoped<ITransactionUnitOfWork>(p => p.GetRequiredService<AppDbContext>());

    services.AddScoped(typeof(IRepository<>), typeof(EfRepository<>))
           .AddScoped(typeof(IReadRepository<>), typeof(EfRepository<>))
           .AddScoped<IListContributorsQueryService, ListContributorsQueryService>()
           .AddScoped<IDeleteContributorService, DeleteContributorService>();

    // 注册RBAC领域服务
    services.AddScoped<IUserService, UserService>()
           .AddScoped<IRoleService, RoleService>()
           .AddScoped<IPermissionService, PermissionService>()
           .AddScoped<IDeptService, DeptService>()
           .AddScoped<IMenuService, MenuService>()
           .AddScoped<ILabAccessKeysService, LabAccessKeysService>();

    // 注册RBAC查询服务
    services.AddScoped<IListUsersQueryService, ListUsersQueryService>()
           .AddScoped<IListRolesQueryService, ListRolesQueryService>()
           .AddScoped<IListPermissionsQueryService, ListPermissionsQueryService>()
           .AddScoped<IListDeptsQueryService, ListDeptsQueryService>()
           .AddScoped<IListMenusQueryService, ListMenusQueryService>();

    // 注册JWT相关服务
    services.Configure<JwtSettings>(config.GetSection("JwtSettings"));
    services.AddScoped<IJwtService, JwtService>();
    services.AddScoped<IRefreshTokenService, RefreshTokenService>();
    services.AddScoped<IAuthService, AuthService>();

    // 注册会话管理服务
    services.Configure<SessionSettings>(config.GetSection("SessionSettings"));
    services.AddScoped<IUserSessionService, UserSessionService>();
    services.AddHostedService<SessionCleanupService>();

    // 配置分布式缓存
    services.AddDistributedMemoryCache();

    logger.LogInformation("{Project} services registered", "Infrastructure");

    return services;
  }
}
