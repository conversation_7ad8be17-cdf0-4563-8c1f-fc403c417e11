﻿{
  "Kestrel": {
    "EndpointDefaults": {
      "Protocols": "Http1"
    },
    "Endpoints": {
      "Http": {
        "Protocols": "Http1",
        "Url": "http://0.0.0.0:5050"
      }
    }
  },
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\v11.0;Database=cleanarchitecture;Trusted_Connection=True;MultipleActiveResultSets=true",
    "SqliteConnection": "Data Source=database.sqlite"
  },
  "Serilog": {
    "MinimumLevel": {
      "Default": "Information"
    },
    "WriteTo": [
      {
        "Name": "Console"
      },
      {
        "Name": "File",
        "Args": {
          "path": "Logs\\log.txt",
          "rollingInterval": "Day"
        }
      }
      //Uncomment this section if you'd like to push your logs to Azure Application Insights
      //Full list of Serilog Sinks can be found here: https://github.com/serilog/serilog/wiki/Provided-Sinks
      //{
      //  "Name": "ApplicationInsights",
      //  "Args": {
      //    "instrumentationKey": "", //Fill in with your ApplicationInsights InstrumentationKey
      //    "telemetryConverter": "Serilog.Sinks.ApplicationInsights.Sinks.ApplicationInsights.TelemetryConverters.TraceTelemetryConverter, Serilog.Sinks.ApplicationInsights"
      //  }
      //}
    ]
  },
  "Mailserver": {
    "Server": "localhost",
    "Port": 25
  },
  "JwtSettings": {
    "Secret": "hBr82kUK2SQz0YREnW7LCXe357O47H90",
    "Issuer": "Witlab.Platform",
    "Audience": "Witlab.Platform.Client",
    "AccessTokenExpirationMinutes": 60,
    "RefreshTokenExpirationDays": 7
  },
  "SessionSettings": {
    "EnableSessionTracking": true,
    "MaxConcurrentSessions": 5,
    "AllowMultipleDeviceLogin": true,
    "SessionTimeoutMinutes": 30,
    "EnableSessionAudit": false,
    "CleanupIntervalMinutes": 300,
    "EnableAnomalyDetection": false,
    "DetectIpChange": false,
    "DetectDeviceChange": false,
    "ActivityUpdateIntervalSeconds": 300,
    "EnableDetailedLogging": false
  },
  "AllowedHosts": "*"
}
