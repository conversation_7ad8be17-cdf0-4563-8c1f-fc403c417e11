using Witlab.Platform.Core.Platform.Events;
using Witlab.Platform.Core.Platform.Services;
using Witlab.Platform.Infrastructure.Notifications.Services;

namespace Witlab.Platform.Infrastructure.Notifications.EventHandlers;

/// <summary>
/// 通知创建事件处理器
/// </summary>
public class NotificationCreatedHandler : INotificationHandler<NotificationCreatedEvent>
{
  private readonly IRealtimeNotificationService _realtimeNotificationService;
  private readonly INotificationCacheService _cacheService;
  private readonly INotificationService _notificationService;
  private readonly ILogger<NotificationCreatedHandler> _logger;

  public NotificationCreatedHandler(
    IRealtimeNotificationService realtimeNotificationService,
    INotificationCacheService cacheService,
    INotificationService notificationService,
    ILogger<NotificationCreatedHandler> logger)
  {
    _realtimeNotificationService = realtimeNotificationService;
    _cacheService = cacheService;
    _notificationService = notificationService;
    _logger = logger;
  }

  public async Task Handle(NotificationCreatedEvent notification, CancellationToken cancellationToken)
  {
    try
    {
      _logger.LogInformation("开始处理通知创建事件，通知ID: {NotificationId}，用户ID: {UserId}", 
        notification.NotificationId, notification.UserId);

      // 获取完整的通知实体
      var notificationResult = await _notificationService.GetNotificationByIdAsync(
        notification.NotificationId, notification.UserId);

      if (!notificationResult.IsSuccess)
      {
        _logger.LogWarning("获取通知实体失败，通知ID: {NotificationId}，错误: {Errors}", 
          notification.NotificationId, string.Join(", ", notificationResult.Errors));
        return;
      }

      var notificationEntity = notificationResult.Value;

      // 发送实时通知
      await _realtimeNotificationService.SendNotificationToUserAsync(
        notification.UserId, notificationEntity);

      // 更新未读数量缓存
      await _cacheService.IncrementUnreadCountAsync(notification.UserId);

      // 发送未读数量更新
      var unreadCount = await _cacheService.GetUnreadCountAsync(notification.UserId);
      if (unreadCount.HasValue)
      {
        await _realtimeNotificationService.SendUnreadCountUpdateAsync(
          notification.UserId, unreadCount.Value);
      }

      _logger.LogInformation("通知创建事件处理完成，通知ID: {NotificationId}，用户ID: {UserId}", 
        notification.NotificationId, notification.UserId);
    }
    catch (Exception ex)
    {
      _logger.LogError(ex, "处理通知创建事件时发生异常，通知ID: {NotificationId}，用户ID: {UserId}", 
        notification.NotificationId, notification.UserId);
    }
  }
}
