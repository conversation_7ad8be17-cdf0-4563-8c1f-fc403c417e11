namespace Witlab.Platform.Core.Platform.Enums;

/// <summary>
/// 通知优先级
/// </summary>
public class NotificationPriority : SmartEnum<NotificationPriority>
{
  /// <summary>
  /// 低优先级
  /// </summary>
  public static readonly NotificationPriority Low = new(nameof(Low), 0);

  /// <summary>
  /// 普通优先级
  /// </summary>
  public static readonly NotificationPriority Normal = new(nameof(Normal), 1);

  /// <summary>
  /// 高优先级
  /// </summary>
  public static readonly NotificationPriority High = new(nameof(High), 2);

  /// <summary>
  /// 紧急优先级
  /// </summary>
  public static readonly NotificationPriority Urgent = new(nameof(Urgent), 3);

  protected NotificationPriority(string name, int value) : base(name, value) { }
}
