using Witlab.Platform.Core.Platform.Enums;

namespace Witlab.Platform.Core.Platform.Events;

/// <summary>
/// 通知创建事件
/// </summary>
public class NotificationCreatedEvent : DomainEventBase
{
  public NotificationCreatedEvent(Guid notificationId, Guid userId, string title, string content, NotificationType type, NotificationCategory category, NotificationPriority priority)
  {
    NotificationId = notificationId;
    UserId = userId;
    Title = title;
    Content = content;
    Type = type;
    Category = category;
    Priority = priority;
  }

  /// <summary>
  /// 通知ID
  /// </summary>
  public Guid NotificationId { get; }

  /// <summary>
  /// 用户ID
  /// </summary>
  public Guid UserId { get; }

  /// <summary>
  /// 通知标题
  /// </summary>
  public string Title { get; }

  /// <summary>
  /// 通知内容
  /// </summary>
  public string Content { get; }

  /// <summary>
  /// 通知类型
  /// </summary>
  public NotificationType Type { get; }

  /// <summary>
  /// 通知分类
  /// </summary>
  public NotificationCategory Category { get; }

  /// <summary>
  /// 优先级
  /// </summary>
  public NotificationPriority Priority { get; }
}
